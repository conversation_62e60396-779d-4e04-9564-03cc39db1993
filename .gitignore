# Large backup and archive files
*.zip
backup.zip
application.zip
update-*.zip
tax_compliance_module.zip
upload.zip

# Logs and temporary files
*.log
error_log
temp/
*.tmp
*.temp

# Cache and session files
application/cache/*
!application/cache/.htaccess
!application/cache/index.html

# Configuration files with sensitive data
application/config/database.php
application/config/app-config.php

# Upload directories (keep structure but ignore content)
uploads/*
!uploads/index.html
!uploads/*/.htaccess
!uploads/*/index.html

# Backup directories
backups/
backup/

# Node modules and package files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Composer vendor directory (keep application/vendor but exclude sensitive files)
vendor/
composer.lock

# Exclude specific sensitive files in vendor
**/sk_test_*
**/sk_live_*
**/*secret*
**/*key*

# Environment files
.env
.env.local
.env.production

# Build files
dist/
build/

# Module specific uploads
modules/*/uploads/*
!modules/*/uploads/index.html

# Custom theme uploads
modules/si_custom_theme/uploads/*
!modules/si_custom_theme/uploads/index.html

# Temporary migration files
*.sql.backup
migration_*.sql

# Error logs
php_errors.log
error.log

# System files
.htaccess.backup
web.config.backup
