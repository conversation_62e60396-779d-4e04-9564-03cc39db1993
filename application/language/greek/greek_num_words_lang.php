<?php

$lang["num_word_1"] ="One";
$lang["num_word_2"] ="Two";
$lang["num_word_3"] ="Three";
$lang["num_word_4"] ="Four";
$lang["num_word_5"] ="Five";
$lang["num_word_6"] ="Six";
$lang["num_word_7"] ="Seven";
$lang["num_word_8"] ="Eight";
$lang["num_word_9"] ="Nine";
$lang["num_word_10"] ="Ten";
$lang["num_word_11"] ="Eleven";
$lang["num_word_12"] ="Twelve";
$lang["num_word_13"] ="Thirteen";
$lang["num_word_14"] ="Fourteen";
$lang["num_word_15"] ="Fifteen";
$lang["num_word_16"] ="Sixteen";
$lang["num_word_17"] ="Seventeen";
$lang["num_word_18"] ="Eighteen";
$lang["num_word_19"] ="Nineteen";
$lang["num_word_20"] ="Twenty";
$lang["num_word_21"] ="Twenty One";
$lang["num_word_22"] ="Twenty Two";
$lang["num_word_23"] ="Twenty Three";
$lang["num_word_24"] ="Twenty Four";
$lang["num_word_25"] ="Twenty Five";
$lang["num_word_26"] ="Twenty Six";
$lang["num_word_27"] ="Twenty Seven";
$lang["num_word_28"] ="Twenty Eight";
$lang["num_word_29"] ="Twenty Nine";
$lang["num_word_30"] ="Thirty";
$lang["num_word_31"] ="Thirty One";
$lang["num_word_32"] ="Thirty Two";
$lang["num_word_33"] ="Thirty Three";
$lang["num_word_34"] ="Thirty Four";
$lang["num_word_35"] ="Thirty Five";
$lang["num_word_36"] ="Thirty Six";
$lang["num_word_37"] ="Thirty Seven";
$lang["num_word_38"] ="Thirty Eight";
$lang["num_word_39"] ="Thirty Nine";
$lang["num_word_40"] ="Forty";
$lang["num_word_41"] ="Forty One";
$lang["num_word_42"] ="Forty Two";
$lang["num_word_43"] ="Forty Three";
$lang["num_word_44"] ="Forty Four";
$lang["num_word_45"] ="Forty Five";
$lang["num_word_46"] ="Forty Six";
$lang["num_word_47"] ="Forty Seven";
$lang["num_word_48"] ="Forty Eight";
$lang["num_word_49"] ="Forty Nine";
$lang["num_word_50"] ="Fifty";
$lang["num_word_51"] ="Fifty One";
$lang["num_word_52"] ="Fifty Two";
$lang["num_word_53"] ="Fifty Three";
$lang["num_word_54"] ="Fifty Four";
$lang["num_word_55"] ="Fifty Five";
$lang["num_word_56"] ="Fifty Six";
$lang["num_word_57"] ="Fifty Seven";
$lang["num_word_58"] ="Fifty Eight";
$lang["num_word_59"] ="Fifty Nine";
$lang["num_word_60"] ="Sixty";
$lang["num_word_61"] ="Sixty One";
$lang["num_word_62"] ="Sixty Two";
$lang["num_word_63"] ="Sixty Three";
$lang["num_word_64"] ="Sixty Four";
$lang["num_word_65"] ="Sixty Five";
$lang["num_word_66"] ="Sixty Six";
$lang["num_word_67"] ="Sixty Seven";
$lang["num_word_68"] ="Sixty Eight";
$lang["num_word_69"] ="Sixty Nine";
$lang["num_word_70"] ="Seventy";
$lang["num_word_71"] ="Seventy One";
$lang["num_word_72"] ="Seventy Two";
$lang["num_word_73"] ="Seventy Three";
$lang["num_word_74"] ="Seventy Four";
$lang["num_word_75"] ="Seventy Five";
$lang["num_word_76"] ="Seventy Six";
$lang["num_word_77"] ="Seventy Seven";
$lang["num_word_78"] ="Seventy Eight";
$lang["num_word_79"] ="Seventy Nine";
$lang["num_word_80"] ="Eighty";
$lang["num_word_81"] ="Eighty One";
$lang["num_word_82"] ="Eighty Two";
$lang["num_word_83"] ="Eighty Three";
$lang["num_word_84"] ="Eighty Four";
$lang["num_word_85"] ="Eighty Five";
$lang["num_word_86"] ="Eighty Six";
$lang["num_word_87"] ="Eighty Seven";
$lang["num_word_88"] ="Eighty Eight";
$lang["num_word_89"] ="Eighty Nine";
$lang["num_word_90"] ="Ninety";
$lang["num_word_91"] ="Ninety One";
$lang["num_word_92"] ="Ninety Two";
$lang["num_word_93"] ="Ninety Three";
$lang["num_word_94"] ="Ninety Four";
$lang["num_word_95"] ="Ninety Five";
$lang["num_word_96"] ="Ninety Six";
$lang["num_word_97"] ="Ninety Seven";
$lang["num_word_98"] ="Ninety Eight";
$lang["num_word_99"] ="Ninety Nine";
$lang["num_word_100"] ="One Hundred";
$lang["num_word_200"] ="Two Hundred";
$lang["num_word_300"] ="Three Hundred";
$lang["num_word_400"] ="Four Hundred";
$lang["num_word_500"] ="Five Hundred";
$lang["num_word_600"] ="Six Hundred";
$lang["num_word_700"] ="Seven Hundred";
$lang["num_word_800"] ="Eight Hundred";
$lang["num_word_900"] ="Nine Hundred";
$lang["num_word_thousand"] ="Thousand";
$lang["num_word_million"] ="Million";
$lang["num_word_billion"] ="Billion";
$lang["num_word_trillion"] ="Trillion";
$lang["num_word_zillion"] ="Zillion";
$lang["num_word_cents"] ="Cents";
$lang["number_word_and"] ="And";

// Show in invoices and estimates
$lang["num_word"] ="With words";

$currencies = array(
    'USD'=>'Dollars',
    'EUR'=>'Euros',
);

$currencies = hooks()->apply_filters('before_number_format_render_languge_currencies',$currencies);

foreach($currencies as $key =>$val){
    $lang['num_word_'.strtoupper($key)] = $val;
}
