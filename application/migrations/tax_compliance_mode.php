<?php defined('BASEPATH') or exit('No direct script access allowed');

class Migration_Tax_compliance_mode extends CI_Migration
{
    public function up()
    {
        // Add the tax compliance mode setting
        add_option('tax_compliance_mode', '0');
        
        // Add setting for when it was activated (timestamp)
        add_option('tax_compliance_mode_activated_at', '');
        
        // Add setting for who activated it (staff ID)
        add_option('tax_compliance_mode_activated_by', '');
        
        // Log the migration
        if (file_exists(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php')) {
            $this->ci->load->helper('file');
            $content = "\n" . 'Tax Compliance Mode migration completed at: ' . date('Y-m-d H:i:s');
            write_file(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php', $content, 'a+');
        }
    }

    public function down()
    {
        // Remove the tax compliance mode settings
        delete_option('tax_compliance_mode');
        delete_option('tax_compliance_mode_activated_at');
        delete_option('tax_compliance_mode_activated_by');
    }
} 