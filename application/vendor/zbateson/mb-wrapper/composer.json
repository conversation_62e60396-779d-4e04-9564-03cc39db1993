{"name": "zbateson/mb-wrapper", "description": "Wrapper for mbstring with fallback to iconv for encoding conversion and string manipulation", "keywords": ["mb_convert_encoding", "charset", "encoding", "string", "mbstring", "iconv", "multibyte", "mb", "mime", "mail", "http"], "license": "BSD-2-<PERSON><PERSON>", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "require": {"php": ">=7.1", "symfony/polyfill-mbstring": "^1.9", "symfony/polyfill-iconv": "^1.9"}, "require-dev": {"phpunit/phpunit": "<10.0", "friendsofphp/php-cs-fixer": "*", "phpstan/phpstan": "*"}, "suggest": {"ext-mbstring": "For best support/performance", "ext-iconv": "For best support/performance"}, "autoload": {"psr-4": {"ZBateson\\MbWrapper\\": "src/"}}, "autoload-dev": {"psr-4": {"ZBateson\\MbWrapper\\": "tests/MbWrapper"}}}