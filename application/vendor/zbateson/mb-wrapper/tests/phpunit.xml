<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    bootstrap="./bootstrap.php"
    colors="true"
    convertDeprecationsToExceptions="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    stopOnError="false"
    stopOnFailure="false"
    stopOnIncomplete="false"
    stopOnSkipped="false"
>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">../src/</directory>
        </whitelist>
    </filter>
    <testsuites>
        <testsuite name="MbWrapper">
            <directory suffix="Test.php">MbWrapper</directory>
        </testsuite>
    </testsuites>
</phpunit>
