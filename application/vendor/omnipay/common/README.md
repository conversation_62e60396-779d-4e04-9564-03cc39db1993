# Omnipay Common

**Core components for the Omnipay PHP payment processing library**

[![PHPUnit tests](https://github.com/thephpleague/omnipay-common/actions/workflows/phpunit.yml/badge.svg)](https://github.com/thephpleague/omnipay-common/actions/workflows/phpunit.yml)
[![Latest Version on Packagist][ico-version]][link-packagist]
[![Software License][ico-license]](LICENSE)
[![Total Downloads][ico-downloads]][link-downloads]

[Omnipay](https://github.com/thephpleague/omnipay) is a framework agnostic, multi-gateway payment
processing library for PHP. This package implements common classes required by Omnipay.

## Documentation

Please see [https://omnipay.thephpleague.com/](https://omnipay.thephpleague.com/) for the installation & usage documentation.

## Change log

Please see [UPGRADE](UPGRADE.md) for more information on how to upgrade to the latest version.

## Support

If you are having general issues with Omnipay, we suggest posting on
[Stack Overflow](https://stackoverflow.com/). Be sure to add the
[omnipay tag](https://stackoverflow.com/questions/tagged/omnipay) so it can be easily found.

If you want to keep up to date with release announcements, discuss ideas for the project,
or ask more detailed questions, there is also a [mailing list](https://groups.google.com/forum/#!forum/omnipay) which
you can subscribe to.

If you believe you have found a bug, please report it using the [GitHub issue tracker](https://github.com/thephpleague/omnipay-common/issues),
or better yet, fork the library and submit a pull request.


## Security

If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.


## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.

[ico-version]: https://img.shields.io/packagist/v/omnipay/common.svg?style=flat
[ico-license]: https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat
[ico-downloads]: https://img.shields.io/packagist/dt/omnipay/common.svg?style=flat

[link-packagist]: https://packagist.org/packages/omnipay/common
[link-downloads]: https://packagist.org/packages/omnipay/common
