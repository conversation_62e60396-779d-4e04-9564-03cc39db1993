{"name": "will<PERSON><PERSON>/email-reply-parser", "type": "library", "description": "Port of the cool GitHub's EmailReplyParser library in PHP", "keywords": ["email", "reply-parser"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.4.0"}, "autoload": {"psr-4": {"EmailReplyParser\\": "src/EmailReplyParser"}}, "autoload-dev": {"psr-4": {"EmailReplyParser\\Tests\\": "tests/EmailReplyParser/Tests"}}, "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require-dev": {"symfony/phpunit-bridge": "^5.0"}}