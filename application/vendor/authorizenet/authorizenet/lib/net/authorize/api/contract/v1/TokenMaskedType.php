<?php

namespace net\authorize\api\contract\v1;

/**
 * Class representing TokenMaskedType
 *
 * 
 * XSD Type: tokenMaskedType
 */
class TokenMaskedType implements \JsonSerializable
{

    /**
     * @property string $tokenSource
     */
    private $tokenSource = null;

    /**
     * @property string $tokenNumber
     */
    private $tokenNumber = null;

    /**
     * @property string $expirationDate
     */
    private $expirationDate = null;

    /**
     * @property string $tokenRequestorId
     */
    private $tokenRequestorId = null;

    /**
     * Gets as tokenSource
     *
     * @return string
     */
    public function getTokenSource()
    {
        return $this->tokenSource;
    }

    /**
     * Sets a new tokenSource
     *
     * @param string $tokenSource
     * @return self
     */
    public function setTokenSource($tokenSource)
    {
        $this->tokenSource = $tokenSource;
        return $this;
    }

    /**
     * Gets as tokenNumber
     *
     * @return string
     */
    public function getTokenNumber()
    {
        return $this->tokenNumber;
    }

    /**
     * Sets a new tokenNumber
     *
     * @param string $tokenNumber
     * @return self
     */
    public function setTokenNumber($tokenNumber)
    {
        $this->tokenNumber = $tokenNumber;
        return $this;
    }

    /**
     * Gets as expirationDate
     *
     * @return string
     */
    public function getExpirationDate()
    {
        return $this->expirationDate;
    }

    /**
     * Sets a new expirationDate
     *
     * @param string $expirationDate
     * @return self
     */
    public function setExpirationDate($expirationDate)
    {
        $this->expirationDate = $expirationDate;
        return $this;
    }

    /**
     * Gets as tokenRequestorId
     *
     * @return string
     */
    public function getTokenRequestorId()
    {
        return $this->tokenRequestorId;
    }

    /**
     * Sets a new tokenRequestorId
     *
     * @param string $tokenRequestorId
     * @return self
     */
    public function setTokenRequestorId($tokenRequestorId)
    {
        $this->tokenRequestorId = $tokenRequestorId;
        return $this;
    }


    // Json Serialize Code
    public function jsonSerialize(){
        $values = array_filter((array)get_object_vars($this),
        function ($val){
            return !is_null($val);
        });
        $mapper = \net\authorize\util\Mapper::Instance();
        foreach($values as $key => $value){
            $classDetails = $mapper->getClass(get_class() , $key);
            if (isset($value)){
                if ($classDetails->className === 'Date'){
                    $dateTime = $value->format('Y-m-d');
                    $values[$key] = $dateTime;
                }
                else if ($classDetails->className === 'DateTime'){
                    $dateTime = $value->format('Y-m-d\TH:i:s\Z');
                    $values[$key] = $dateTime;
                }
                if (is_array($value)){
                    if (!$classDetails->isInlineArray){
                        $subKey = $classDetails->arrayEntryname;
                        $subArray = [$subKey => $value];
                        $values[$key] = $subArray;
                    }
                }
            }
        }
        return $values;
    }
    
    // Json Set Code
    public function set($data)
    {
        if(is_array($data) || is_object($data)) {
			$mapper = \net\authorize\util\Mapper::Instance();
			foreach($data AS $key => $value) {
				$classDetails = $mapper->getClass(get_class() , $key);
	 
				if($classDetails !== NULL ) {
					if ($classDetails->isArray) {
						if ($classDetails->isCustomDefined) {
							foreach($value AS $keyChild => $valueChild) {
								$type = new $classDetails->className;
								$type->set($valueChild);
								$this->{'addTo' . $key}($type);
							}
						}
						else if ($classDetails->className === 'DateTime' || $classDetails->className === 'Date' ) {
							foreach($value AS $keyChild => $valueChild) {
								$type = new \DateTime($valueChild);
								$this->{'addTo' . $key}($type);
							}
						}
						else {
							foreach($value AS $keyChild => $valueChild) {
								$this->{'addTo' . $key}($valueChild);
							}
						}
					}
					else {
						if ($classDetails->isCustomDefined){
							$type = new $classDetails->className;
							$type->set($value);
							$this->{'set' . $key}($type);
						}
						else if ($classDetails->className === 'DateTime' || $classDetails->className === 'Date' ) {
							$type = new \DateTime($value);
							$this->{'set' . $key}($type);
						}
						else {
							$this->{'set' . $key}($value);
						}
					}
				}
			}
		}
    }
    
}

