<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Address;

use <PERSON><PERSON>lio\Http\Response;
use <PERSON>wi<PERSON>\Page;
use <PERSON>wilio\Version;

class DependentPhoneNumberPage extends Page {
    /**
     * @param Version $version Version that contains the resource
     * @param Response $response Response from the API
     * @param array $solution The context solution
     */
    public function __construct(Version $version, Response $response, array $solution) {
        parent::__construct($version, $response);

        // Path Solution
        $this->solution = $solution;
    }

    /**
     * @param array $payload Payload response from the API
     * @return DependentPhoneNumberInstance \Twilio\Rest\Api\V2010\Account\Address\DependentPhoneNumberInstance
     */
    public function buildInstance(array $payload): DependentPhoneNumberInstance {
        return new DependentPhoneNumberInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['addressSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Api.V2010.DependentPhoneNumberPage]';
    }
}