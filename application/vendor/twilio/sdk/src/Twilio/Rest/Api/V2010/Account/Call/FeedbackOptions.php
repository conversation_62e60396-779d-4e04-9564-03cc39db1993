<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON><PERSON><PERSON>\Values;

abstract class FeedbackOptions {
    /**
     * @param string[] $issue Issues experienced during the call
     * @return CreateFeedbackOptions Options builder
     */
    public static function create(array $issue = Values::ARRAY_NONE): CreateFeedbackOptions {
        return new CreateFeedbackOptions($issue);
    }

    /**
     * @param int $qualityScore The call quality expressed as an integer from 1 to 5
     * @param string[] $issue Issues experienced during the call
     * @return UpdateFeedbackOptions Options builder
     */
    public static function update(int $qualityScore = Values::NONE, array $issue = Values::ARRAY_NONE): UpdateFeedbackOptions {
        return new UpdateFeedbackOptions($qualityScore, $issue);
    }
}

class CreateFeedbackOptions extends Options {
    /**
     * @param string[] $issue Issues experienced during the call
     */
    public function __construct(array $issue = Values::ARRAY_NONE) {
        $this->options['issue'] = $issue;
    }

    /**
     * A list of one or more issues experienced during the call. Issues can be: `imperfect-audio`, `dropped-call`, `incorrect-caller-id`, `post-dial-delay`, `digits-not-captured`, `audio-latency`, `unsolicited-call`, or `one-way-audio`.
     *
     * @param string[] $issue Issues experienced during the call
     * @return $this Fluent Builder
     */
    public function setIssue(array $issue): self {
        $this->options['issue'] = $issue;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateFeedbackOptions ' . $options . ']';
    }
}

class UpdateFeedbackOptions extends Options {
    /**
     * @param int $qualityScore The call quality expressed as an integer from 1 to 5
     * @param string[] $issue Issues experienced during the call
     */
    public function __construct(int $qualityScore = Values::NONE, array $issue = Values::ARRAY_NONE) {
        $this->options['qualityScore'] = $qualityScore;
        $this->options['issue'] = $issue;
    }

    /**
     * The call quality expressed as an integer from `1` to `5` where `1` represents very poor call quality and `5` represents a perfect call.
     *
     * @param int $qualityScore The call quality expressed as an integer from 1 to 5
     * @return $this Fluent Builder
     */
    public function setQualityScore(int $qualityScore): self {
        $this->options['qualityScore'] = $qualityScore;
        return $this;
    }

    /**
     * One or more issues experienced during the call. The issues can be: `imperfect-audio`, `dropped-call`, `incorrect-caller-id`, `post-dial-delay`, `digits-not-captured`, `audio-latency`, `unsolicited-call`, or `one-way-audio`.
     *
     * @param string[] $issue Issues experienced during the call
     * @return $this Fluent Builder
     */
    public function setIssue(array $issue): self {
        $this->options['issue'] = $issue;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.UpdateFeedbackOptions ' . $options . ']';
    }
}