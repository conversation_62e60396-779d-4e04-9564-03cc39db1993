<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Exceptions\TwilioException;
use T<PERSON>lio\InstanceContext;
use Twilio\Values;
use Twilio\Version;

class FeedbackSummaryContext extends InstanceContext {
    /**
     * Initialize the FeedbackSummaryContext
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The unique sid that identifies this account
     * @param string $sid A string that uniquely identifies this feedback summary
     *                    resource
     */
    public function __construct(Version $version, $accountSid, $sid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['accountSid' => $accountSid, 'sid' => $sid, ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid) . '/Calls/FeedbackSummary/' . \rawurlencode($sid) . '.json';
    }

    /**
     * Fetch the FeedbackSummaryInstance
     *
     * @return FeedbackSummaryInstance Fetched FeedbackSummaryInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FeedbackSummaryInstance {
        $payload = $this->version->fetch('GET', $this->uri);

        return new FeedbackSummaryInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }

    /**
     * Delete the FeedbackSummaryInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool {
        return $this->version->delete('DELETE', $this->uri);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.FeedbackSummaryContext ' . \implode(' ', $context) . ']';
    }
}