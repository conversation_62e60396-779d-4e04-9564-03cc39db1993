<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\ListResource;
use Twi<PERSON>\Options;
use Twilio\Values;
use Twilio\Version;

class UserDefinedMessageSubscriptionList extends ListResource {
    /**
     * Construct the UserDefinedMessageSubscriptionList
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid Account SID.
     * @param string $callSid Call SID.
     */
    public function __construct(Version $version, string $accountSid, string $callSid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['accountSid' => $accountSid, 'callSid' => $callSid, ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid) . '/Calls/' . \rawurlencode($callSid) . '/UserDefinedMessageSubscriptions.json';
    }

    /**
     * Create the UserDefinedMessageSubscriptionInstance
     *
     * @param string $callback The URL we should call to send user defined messages.
     * @param array|Options $options Optional Arguments
     * @return UserDefinedMessageSubscriptionInstance Created
     *                                                UserDefinedMessageSubscriptionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $callback, array $options = []): UserDefinedMessageSubscriptionInstance {
        $options = new Values($options);

        $data = Values::of([
            'Callback' => $callback,
            'IdempotencyKey' => $options['idempotencyKey'],
            'Method' => $options['method'],
        ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new UserDefinedMessageSubscriptionInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['callSid']
        );
    }

    /**
     * Constructs a UserDefinedMessageSubscriptionContext
     *
     * @param string $sid User Defined Message Subscription SID.
     */
    public function getContext(string $sid): UserDefinedMessageSubscriptionContext {
        return new UserDefinedMessageSubscriptionContext(
            $this->version,
            $this->solution['accountSid'],
            $this->solution['callSid'],
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Api.V2010.UserDefinedMessageSubscriptionList]';
    }
}