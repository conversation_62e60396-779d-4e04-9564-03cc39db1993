<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON><PERSON><PERSON>\Values;

abstract class NotificationOptions {
    /**
     * @param int $log Filter by log level
     * @param string $messageDateBefore Filter by date
     * @param string $messageDate Filter by date
     * @param string $messageDateAfter Filter by date
     * @return ReadNotificationOptions Options builder
     */
    public static function read(int $log = Values::NONE, string $messageDateBefore = Values::NONE, string $messageDate = Values::NONE, string $messageDateAfter = Values::NONE): ReadNotificationOptions {
        return new ReadNotificationOptions($log, $messageDateBefore, $messageDate, $messageDateAfter);
    }
}

class ReadNotificationOptions extends Options {
    /**
     * @param int $log Filter by log level
     * @param string $messageDateBefore Filter by date
     * @param string $messageDate Filter by date
     * @param string $messageDateAfter Filter by date
     */
    public function __construct(int $log = Values::NONE, string $messageDateBefore = Values::NONE, string $messageDate = Values::NONE, string $messageDateAfter = Values::NONE) {
        $this->options['log'] = $log;
        $this->options['messageDateBefore'] = $messageDateBefore;
        $this->options['messageDate'] = $messageDate;
        $this->options['messageDateAfter'] = $messageDateAfter;
    }

    /**
     * Only read notifications of the specified log level. Can be:  `0` to read only ERROR notifications or `1` to read only WARNING notifications. By default, all notifications are read.
     *
     * @param int $log Filter by log level
     * @return $this Fluent Builder
     */
    public function setLog(int $log): self {
        $this->options['log'] = $log;
        return $this;
    }

    /**
     * Only show notifications for the specified date, formatted as `YYYY-MM-DD`. You can also specify an inequality, such as `<=YYYY-MM-DD` for messages logged at or before midnight on a date, or `>=YYYY-MM-DD` for messages logged at or after midnight on a date.
     *
     * @param string $messageDateBefore Filter by date
     * @return $this Fluent Builder
     */
    public function setMessageDateBefore(string $messageDateBefore): self {
        $this->options['messageDateBefore'] = $messageDateBefore;
        return $this;
    }

    /**
     * Only show notifications for the specified date, formatted as `YYYY-MM-DD`. You can also specify an inequality, such as `<=YYYY-MM-DD` for messages logged at or before midnight on a date, or `>=YYYY-MM-DD` for messages logged at or after midnight on a date.
     *
     * @param string $messageDate Filter by date
     * @return $this Fluent Builder
     */
    public function setMessageDate(string $messageDate): self {
        $this->options['messageDate'] = $messageDate;
        return $this;
    }

    /**
     * Only show notifications for the specified date, formatted as `YYYY-MM-DD`. You can also specify an inequality, such as `<=YYYY-MM-DD` for messages logged at or before midnight on a date, or `>=YYYY-MM-DD` for messages logged at or after midnight on a date.
     *
     * @param string $messageDateAfter Filter by date
     * @return $this Fluent Builder
     */
    public function setMessageDateAfter(string $messageDateAfter): self {
        $this->options['messageDateAfter'] = $messageDateAfter;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.ReadNotificationOptions ' . $options . ']';
    }
}