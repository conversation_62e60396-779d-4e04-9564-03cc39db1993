<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twi<PERSON>\Exceptions\TwilioException;
use <PERSON>wilio\InstanceContext;
use Twilio\Version;

class UserDefinedMessageSubscriptionContext extends InstanceContext {
    /**
     * Initialize the UserDefinedMessageSubscriptionContext
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid Account SID.
     * @param string $callSid Call SID.
     * @param string $sid User Defined Message Subscription SID.
     */
    public function __construct(Version $version, $accountSid, $callSid, $sid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['accountSid' => $accountSid, 'callSid' => $callSid, 'sid' => $sid, ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid) . '/Calls/' . \rawurlencode($callSid) . '/UserDefinedMessageSubscriptions/' . \rawurlencode($sid) . '.json';
    }

    /**
     * Delete the UserDefinedMessageSubscriptionInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool {
        return $this->version->delete('DELETE', $this->uri);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.UserDefinedMessageSubscriptionContext ' . \implode(' ', $context) . ']';
    }
}