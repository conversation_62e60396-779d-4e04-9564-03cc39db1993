<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use <PERSON><PERSON><PERSON>\Deserialize;
use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;

/**
 * PLEASE NOTE that this class contains preview products that are subject to change. Use them with caution. If you currently do not have developer preview access, <NAME_EMAIL>.
 *
 * @property string $accountSid
 * @property string $callSid
 * @property string $sid
 * @property \DateTime $dateCreated
 * @property \DateTime $dateUpdated
 * @property string $uri
 */
class PaymentInstance extends InstanceResource {
    /**
     * Initialize the PaymentInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $accountSid The SID of the Account that created the Payments
     *                           resource.
     * @param string $callSid The SID of the Call the resource is associated with.
     * @param string $sid The SID of Payments session
     */
    public function __construct(Version $version, array $payload, string $accountSid, string $callSid, string $sid = null) {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'callSid' => Values::array_get($payload, 'call_sid'),
            'sid' => Values::array_get($payload, 'sid'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'uri' => Values::array_get($payload, 'uri'),
        ];

        $this->solution = [
            'accountSid' => $accountSid,
            'callSid' => $callSid,
            'sid' => $sid ?: $this->properties['sid'],
        ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return PaymentContext Context for this PaymentInstance
     */
    protected function proxy(): PaymentContext {
        if (!$this->context) {
            $this->context = new PaymentContext(
                $this->version,
                $this->solution['accountSid'],
                $this->solution['callSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Update the PaymentInstance
     *
     * @param string $idempotencyKey A unique token that will be used to ensure
     *                               that multiple API calls with the same
     *                               information do not result in multiple
     *                               transactions.
     * @param string $statusCallback Provide an absolute or relative URL to receive
     *                               status updates regarding your Pay session.
     * @param array|Options $options Optional Arguments
     * @return PaymentInstance Updated PaymentInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $idempotencyKey, string $statusCallback, array $options = []): PaymentInstance {
        return $this->proxy()->update($idempotencyKey, $statusCallback, $options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name) {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.PaymentInstance ' . \implode(' ', $context) . ']';
    }
}