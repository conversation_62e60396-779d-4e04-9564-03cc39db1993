<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account\Conference;

use <PERSON><PERSON><PERSON>\Options;
use Twi<PERSON>\Values;

abstract class ParticipantOptions {
    /**
     * @param bool $muted Whether the participant should be muted
     * @param bool $hold Whether the participant should be on hold
     * @param string $holdUrl The URL we call using the `hold_method` for music
     *                        that plays when the participant is on hold
     * @param string $holdMethod The HTTP method we should use to call hold_url
     * @param string $announceUrl The URL we call using the `announce_method` for
     *                            an announcement to the participant
     * @param string $announceMethod The HTTP method we should use to call
     *                               announce_url
     * @param string $waitUrl The URL we call using the `wait_method` for the music
     *                        to play while participants are waiting for the
     *                        conference to start
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @param bool $beepOnExit Whether to play a notification beep to the
     *                         conference when the participant exit
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  participant leaves
     * @param bool $coaching Indicates if the participant changed to coach
     * @param string $callSidToCoach The SID of the participant who is being
     *                               `coached`
     * @return UpdateParticipantOptions Options builder
     */
    public static function update(bool $muted = Values::NONE, bool $hold = Values::NONE, string $holdUrl = Values::NONE, string $holdMethod = Values::NONE, string $announceUrl = Values::NONE, string $announceMethod = Values::NONE, string $waitUrl = Values::NONE, string $waitMethod = Values::NONE, bool $beepOnExit = Values::NONE, bool $endConferenceOnExit = Values::NONE, bool $coaching = Values::NONE, string $callSidToCoach = Values::NONE): UpdateParticipantOptions {
        return new UpdateParticipantOptions($muted, $hold, $holdUrl, $holdMethod, $announceUrl, $announceMethod, $waitUrl, $waitMethod, $beepOnExit, $endConferenceOnExit, $coaching, $callSidToCoach);
    }

    /**
     * @param string $statusCallback The URL we should call to send status
     *                               information to your application
     * @param string $statusCallbackMethod The HTTP method we should use to call
     *                                     `status_callback`
     * @param string[] $statusCallbackEvent Set state change events that will
     *                                      trigger a callback
     * @param string $label The label of this participant
     * @param int $timeout he number of seconds that we should wait for an answer
     * @param bool $record Whether to record the participant and their conferences
     * @param bool $muted Whether to mute the agent
     * @param string $beep Whether to play a notification beep to the conference
     *                     when the participant joins
     * @param bool $startConferenceOnEnter Whether the conference starts when the
     *                                     participant joins the conference
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  participant leaves
     * @param string $waitUrl URL that hosts pre-conference hold music
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @param bool $earlyMedia Whether agents can hear the state of the outbound
     *                         call
     * @param int $maxParticipants The maximum number of agent conference
     *                             participants
     * @param string $conferenceRecord Whether to record the conference the
     *                                 participant is joining
     * @param string $conferenceTrim Whether to trim leading and trailing silence
     *                               from your recorded conference audio files
     * @param string $conferenceStatusCallback The callback URL for conference
     *                                         events
     * @param string $conferenceStatusCallbackMethod HTTP method for requesting
     *                                               `conference_status_callback`
     *                                               URL
     * @param string[] $conferenceStatusCallbackEvent The conference state changes
     *                                                that should generate a call
     *                                                to
     *                                                `conference_status_callback`
     * @param string $recordingChannels Specify `mono` or `dual` recording channels
     * @param string $recordingStatusCallback The URL that we should call using the
     *                                        `recording_status_callback_method`
     *                                        when the recording status changes
     * @param string $recordingStatusCallbackMethod The HTTP method we should use
     *                                              when we call
     *                                              `recording_status_callback`
     * @param string $sipAuthUsername The SIP username used for authentication
     * @param string $sipAuthPassword The SIP password for authentication
     * @param string $region The region where we should mix the conference audio
     * @param string $conferenceRecordingStatusCallback The URL we should call
     *                                                  using the
     *                                                  `conference_recording_status_callback_method` when the conference recording is available
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we
     *                                                        should use to call
     *                                                        `conference_recording_status_callback`
     * @param string[] $recordingStatusCallbackEvent The recording state changes
     *                                               that should generate a call to
     *                                               `recording_status_callback`
     * @param string[] $conferenceRecordingStatusCallbackEvent The conference
     *                                                         recording state
     *                                                         changes that should
     *                                                         generate a call to
     *                                                         `conference_recording_status_callback`
     * @param bool $coaching Indicates if the participant changed to coach
     * @param string $callSidToCoach The SID of the participant who is being
     *                               `coached`
     * @param string $jitterBufferSize Jitter Buffer size for the connecting
     *                                 participant
     * @param string $byoc BYOC trunk SID (Beta)
     * @param string $callerId The phone number, Client identifier, or username
     *                         portion of SIP address that made this call.
     * @param string $callReason Reason for the call (Branded Calls Beta)
     * @param string $recordingTrack The track(s) to record
     * @param int $timeLimit The maximum duration of the call in seconds.
     * @param string $machineDetection Enable machine detection or end of greeting
     *                                 detection
     * @param int $machineDetectionTimeout Number of seconds to wait for machine
     *                                     detection
     * @param int $machineDetectionSpeechThreshold Number of milliseconds for
     *                                             measuring stick for the length
     *                                             of the speech activity
     * @param int $machineDetectionSpeechEndThreshold Number of milliseconds of
     *                                                silence after speech activity
     * @param int $machineDetectionSilenceTimeout Number of milliseconds of initial
     *                                            silence
     * @param string $amdStatusCallback The URL we should call to send amd status
     *                                  information to your application
     * @param string $amdStatusCallbackMethod HTTP Method to use with
     *                                        amd_status_callback
     * @return CreateParticipantOptions Options builder
     */
    public static function create(string $statusCallback = Values::NONE, string $statusCallbackMethod = Values::NONE, array $statusCallbackEvent = Values::ARRAY_NONE, string $label = Values::NONE, int $timeout = Values::NONE, bool $record = Values::NONE, bool $muted = Values::NONE, string $beep = Values::NONE, bool $startConferenceOnEnter = Values::NONE, bool $endConferenceOnExit = Values::NONE, string $waitUrl = Values::NONE, string $waitMethod = Values::NONE, bool $earlyMedia = Values::NONE, int $maxParticipants = Values::NONE, string $conferenceRecord = Values::NONE, string $conferenceTrim = Values::NONE, string $conferenceStatusCallback = Values::NONE, string $conferenceStatusCallbackMethod = Values::NONE, array $conferenceStatusCallbackEvent = Values::ARRAY_NONE, string $recordingChannels = Values::NONE, string $recordingStatusCallback = Values::NONE, string $recordingStatusCallbackMethod = Values::NONE, string $sipAuthUsername = Values::NONE, string $sipAuthPassword = Values::NONE, string $region = Values::NONE, string $conferenceRecordingStatusCallback = Values::NONE, string $conferenceRecordingStatusCallbackMethod = Values::NONE, array $recordingStatusCallbackEvent = Values::ARRAY_NONE, array $conferenceRecordingStatusCallbackEvent = Values::ARRAY_NONE, bool $coaching = Values::NONE, string $callSidToCoach = Values::NONE, string $jitterBufferSize = Values::NONE, string $byoc = Values::NONE, string $callerId = Values::NONE, string $callReason = Values::NONE, string $recordingTrack = Values::NONE, int $timeLimit = Values::NONE, string $machineDetection = Values::NONE, int $machineDetectionTimeout = Values::NONE, int $machineDetectionSpeechThreshold = Values::NONE, int $machineDetectionSpeechEndThreshold = Values::NONE, int $machineDetectionSilenceTimeout = Values::NONE, string $amdStatusCallback = Values::NONE, string $amdStatusCallbackMethod = Values::NONE): CreateParticipantOptions {
        return new CreateParticipantOptions($statusCallback, $statusCallbackMethod, $statusCallbackEvent, $label, $timeout, $record, $muted, $beep, $startConferenceOnEnter, $endConferenceOnExit, $waitUrl, $waitMethod, $earlyMedia, $maxParticipants, $conferenceRecord, $conferenceTrim, $conferenceStatusCallback, $conferenceStatusCallbackMethod, $conferenceStatusCallbackEvent, $recordingChannels, $recordingStatusCallback, $recordingStatusCallbackMethod, $sipAuthUsername, $sipAuthPassword, $region, $conferenceRecordingStatusCallback, $conferenceRecordingStatusCallbackMethod, $recordingStatusCallbackEvent, $conferenceRecordingStatusCallbackEvent, $coaching, $callSidToCoach, $jitterBufferSize, $byoc, $callerId, $callReason, $recordingTrack, $timeLimit, $machineDetection, $machineDetectionTimeout, $machineDetectionSpeechThreshold, $machineDetectionSpeechEndThreshold, $machineDetectionSilenceTimeout, $amdStatusCallback, $amdStatusCallbackMethod);
    }

    /**
     * @param bool $muted Whether to return only participants that are muted
     * @param bool $hold Whether to return only participants that are on hold
     * @param bool $coaching Whether to return only participants who are coaching
     *                       another call
     * @return ReadParticipantOptions Options builder
     */
    public static function read(bool $muted = Values::NONE, bool $hold = Values::NONE, bool $coaching = Values::NONE): ReadParticipantOptions {
        return new ReadParticipantOptions($muted, $hold, $coaching);
    }
}

class UpdateParticipantOptions extends Options {
    /**
     * @param bool $muted Whether the participant should be muted
     * @param bool $hold Whether the participant should be on hold
     * @param string $holdUrl The URL we call using the `hold_method` for music
     *                        that plays when the participant is on hold
     * @param string $holdMethod The HTTP method we should use to call hold_url
     * @param string $announceUrl The URL we call using the `announce_method` for
     *                            an announcement to the participant
     * @param string $announceMethod The HTTP method we should use to call
     *                               announce_url
     * @param string $waitUrl The URL we call using the `wait_method` for the music
     *                        to play while participants are waiting for the
     *                        conference to start
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @param bool $beepOnExit Whether to play a notification beep to the
     *                         conference when the participant exit
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  participant leaves
     * @param bool $coaching Indicates if the participant changed to coach
     * @param string $callSidToCoach The SID of the participant who is being
     *                               `coached`
     */
    public function __construct(bool $muted = Values::NONE, bool $hold = Values::NONE, string $holdUrl = Values::NONE, string $holdMethod = Values::NONE, string $announceUrl = Values::NONE, string $announceMethod = Values::NONE, string $waitUrl = Values::NONE, string $waitMethod = Values::NONE, bool $beepOnExit = Values::NONE, bool $endConferenceOnExit = Values::NONE, bool $coaching = Values::NONE, string $callSidToCoach = Values::NONE) {
        $this->options['muted'] = $muted;
        $this->options['hold'] = $hold;
        $this->options['holdUrl'] = $holdUrl;
        $this->options['holdMethod'] = $holdMethod;
        $this->options['announceUrl'] = $announceUrl;
        $this->options['announceMethod'] = $announceMethod;
        $this->options['waitUrl'] = $waitUrl;
        $this->options['waitMethod'] = $waitMethod;
        $this->options['beepOnExit'] = $beepOnExit;
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        $this->options['coaching'] = $coaching;
        $this->options['callSidToCoach'] = $callSidToCoach;
    }

    /**
     * Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
     *
     * @param bool $muted Whether the participant should be muted
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
     *
     * @param bool $hold Whether the participant should be on hold
     * @return $this Fluent Builder
     */
    public function setHold(bool $hold): self {
        $this->options['hold'] = $hold;
        return $this;
    }

    /**
     * The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     *
     * @param string $holdUrl The URL we call using the `hold_method` for music
     *                        that plays when the participant is on hold
     * @return $this Fluent Builder
     */
    public function setHoldUrl(string $holdUrl): self {
        $this->options['holdUrl'] = $holdUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
     *
     * @param string $holdMethod The HTTP method we should use to call hold_url
     * @return $this Fluent Builder
     */
    public function setHoldMethod(string $holdMethod): self {
        $this->options['holdMethod'] = $holdMethod;
        return $this;
    }

    /**
     * The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     *
     * @param string $announceUrl The URL we call using the `announce_method` for
     *                            an announcement to the participant
     * @return $this Fluent Builder
     */
    public function setAnnounceUrl(string $announceUrl): self {
        $this->options['announceUrl'] = $announceUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $announceMethod The HTTP method we should use to call
     *                               announce_url
     * @return $this Fluent Builder
     */
    public function setAnnounceMethod(string $announceMethod): self {
        $this->options['announceMethod'] = $announceMethod;
        return $this;
    }

    /**
     * The URL we call using the `wait_method` for the music to play while participants are waiting for the conference to start. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     *
     * @param string $waitUrl The URL we call using the `wait_method` for the music
     *                        to play while participants are waiting for the
     *                        conference to start
     * @return $this Fluent Builder
     */
    public function setWaitUrl(string $waitUrl): self {
        $this->options['waitUrl'] = $waitUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     *
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @return $this Fluent Builder
     */
    public function setWaitMethod(string $waitMethod): self {
        $this->options['waitMethod'] = $waitMethod;
        return $this;
    }

    /**
     * Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
     *
     * @param bool $beepOnExit Whether to play a notification beep to the
     *                         conference when the participant exit
     * @return $this Fluent Builder
     */
    public function setBeepOnExit(bool $beepOnExit): self {
        $this->options['beepOnExit'] = $beepOnExit;
        return $this;
    }

    /**
     * Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     *
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  participant leaves
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnExit(bool $endConferenceOnExit): self {
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        return $this;
    }

    /**
     * Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     *
     * @param bool $coaching Indicates if the participant changed to coach
     * @return $this Fluent Builder
     */
    public function setCoaching(bool $coaching): self {
        $this->options['coaching'] = $coaching;
        return $this;
    }

    /**
     * The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     *
     * @param string $callSidToCoach The SID of the participant who is being
     *                               `coached`
     * @return $this Fluent Builder
     */
    public function setCallSidToCoach(string $callSidToCoach): self {
        $this->options['callSidToCoach'] = $callSidToCoach;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.UpdateParticipantOptions ' . $options . ']';
    }
}

class CreateParticipantOptions extends Options {
    /**
     * @param string $statusCallback The URL we should call to send status
     *                               information to your application
     * @param string $statusCallbackMethod The HTTP method we should use to call
     *                                     `status_callback`
     * @param string[] $statusCallbackEvent Set state change events that will
     *                                      trigger a callback
     * @param string $label The label of this participant
     * @param int $timeout he number of seconds that we should wait for an answer
     * @param bool $record Whether to record the participant and their conferences
     * @param bool $muted Whether to mute the agent
     * @param string $beep Whether to play a notification beep to the conference
     *                     when the participant joins
     * @param bool $startConferenceOnEnter Whether the conference starts when the
     *                                     participant joins the conference
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  participant leaves
     * @param string $waitUrl URL that hosts pre-conference hold music
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @param bool $earlyMedia Whether agents can hear the state of the outbound
     *                         call
     * @param int $maxParticipants The maximum number of agent conference
     *                             participants
     * @param string $conferenceRecord Whether to record the conference the
     *                                 participant is joining
     * @param string $conferenceTrim Whether to trim leading and trailing silence
     *                               from your recorded conference audio files
     * @param string $conferenceStatusCallback The callback URL for conference
     *                                         events
     * @param string $conferenceStatusCallbackMethod HTTP method for requesting
     *                                               `conference_status_callback`
     *                                               URL
     * @param string[] $conferenceStatusCallbackEvent The conference state changes
     *                                                that should generate a call
     *                                                to
     *                                                `conference_status_callback`
     * @param string $recordingChannels Specify `mono` or `dual` recording channels
     * @param string $recordingStatusCallback The URL that we should call using the
     *                                        `recording_status_callback_method`
     *                                        when the recording status changes
     * @param string $recordingStatusCallbackMethod The HTTP method we should use
     *                                              when we call
     *                                              `recording_status_callback`
     * @param string $sipAuthUsername The SIP username used for authentication
     * @param string $sipAuthPassword The SIP password for authentication
     * @param string $region The region where we should mix the conference audio
     * @param string $conferenceRecordingStatusCallback The URL we should call
     *                                                  using the
     *                                                  `conference_recording_status_callback_method` when the conference recording is available
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we
     *                                                        should use to call
     *                                                        `conference_recording_status_callback`
     * @param string[] $recordingStatusCallbackEvent The recording state changes
     *                                               that should generate a call to
     *                                               `recording_status_callback`
     * @param string[] $conferenceRecordingStatusCallbackEvent The conference
     *                                                         recording state
     *                                                         changes that should
     *                                                         generate a call to
     *                                                         `conference_recording_status_callback`
     * @param bool $coaching Indicates if the participant changed to coach
     * @param string $callSidToCoach The SID of the participant who is being
     *                               `coached`
     * @param string $jitterBufferSize Jitter Buffer size for the connecting
     *                                 participant
     * @param string $byoc BYOC trunk SID (Beta)
     * @param string $callerId The phone number, Client identifier, or username
     *                         portion of SIP address that made this call.
     * @param string $callReason Reason for the call (Branded Calls Beta)
     * @param string $recordingTrack The track(s) to record
     * @param int $timeLimit The maximum duration of the call in seconds.
     * @param string $machineDetection Enable machine detection or end of greeting
     *                                 detection
     * @param int $machineDetectionTimeout Number of seconds to wait for machine
     *                                     detection
     * @param int $machineDetectionSpeechThreshold Number of milliseconds for
     *                                             measuring stick for the length
     *                                             of the speech activity
     * @param int $machineDetectionSpeechEndThreshold Number of milliseconds of
     *                                                silence after speech activity
     * @param int $machineDetectionSilenceTimeout Number of milliseconds of initial
     *                                            silence
     * @param string $amdStatusCallback The URL we should call to send amd status
     *                                  information to your application
     * @param string $amdStatusCallbackMethod HTTP Method to use with
     *                                        amd_status_callback
     */
    public function __construct(string $statusCallback = Values::NONE, string $statusCallbackMethod = Values::NONE, array $statusCallbackEvent = Values::ARRAY_NONE, string $label = Values::NONE, int $timeout = Values::NONE, bool $record = Values::NONE, bool $muted = Values::NONE, string $beep = Values::NONE, bool $startConferenceOnEnter = Values::NONE, bool $endConferenceOnExit = Values::NONE, string $waitUrl = Values::NONE, string $waitMethod = Values::NONE, bool $earlyMedia = Values::NONE, int $maxParticipants = Values::NONE, string $conferenceRecord = Values::NONE, string $conferenceTrim = Values::NONE, string $conferenceStatusCallback = Values::NONE, string $conferenceStatusCallbackMethod = Values::NONE, array $conferenceStatusCallbackEvent = Values::ARRAY_NONE, string $recordingChannels = Values::NONE, string $recordingStatusCallback = Values::NONE, string $recordingStatusCallbackMethod = Values::NONE, string $sipAuthUsername = Values::NONE, string $sipAuthPassword = Values::NONE, string $region = Values::NONE, string $conferenceRecordingStatusCallback = Values::NONE, string $conferenceRecordingStatusCallbackMethod = Values::NONE, array $recordingStatusCallbackEvent = Values::ARRAY_NONE, array $conferenceRecordingStatusCallbackEvent = Values::ARRAY_NONE, bool $coaching = Values::NONE, string $callSidToCoach = Values::NONE, string $jitterBufferSize = Values::NONE, string $byoc = Values::NONE, string $callerId = Values::NONE, string $callReason = Values::NONE, string $recordingTrack = Values::NONE, int $timeLimit = Values::NONE, string $machineDetection = Values::NONE, int $machineDetectionTimeout = Values::NONE, int $machineDetectionSpeechThreshold = Values::NONE, int $machineDetectionSpeechEndThreshold = Values::NONE, int $machineDetectionSilenceTimeout = Values::NONE, string $amdStatusCallback = Values::NONE, string $amdStatusCallbackMethod = Values::NONE) {
        $this->options['statusCallback'] = $statusCallback;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        $this->options['label'] = $label;
        $this->options['timeout'] = $timeout;
        $this->options['record'] = $record;
        $this->options['muted'] = $muted;
        $this->options['beep'] = $beep;
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        $this->options['waitUrl'] = $waitUrl;
        $this->options['waitMethod'] = $waitMethod;
        $this->options['earlyMedia'] = $earlyMedia;
        $this->options['maxParticipants'] = $maxParticipants;
        $this->options['conferenceRecord'] = $conferenceRecord;
        $this->options['conferenceTrim'] = $conferenceTrim;
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        $this->options['recordingChannels'] = $recordingChannels;
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        $this->options['region'] = $region;
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        $this->options['recordingStatusCallbackEvent'] = $recordingStatusCallbackEvent;
        $this->options['conferenceRecordingStatusCallbackEvent'] = $conferenceRecordingStatusCallbackEvent;
        $this->options['coaching'] = $coaching;
        $this->options['callSidToCoach'] = $callSidToCoach;
        $this->options['jitterBufferSize'] = $jitterBufferSize;
        $this->options['byoc'] = $byoc;
        $this->options['callerId'] = $callerId;
        $this->options['callReason'] = $callReason;
        $this->options['recordingTrack'] = $recordingTrack;
        $this->options['timeLimit'] = $timeLimit;
        $this->options['machineDetection'] = $machineDetection;
        $this->options['machineDetectionTimeout'] = $machineDetectionTimeout;
        $this->options['machineDetectionSpeechThreshold'] = $machineDetectionSpeechThreshold;
        $this->options['machineDetectionSpeechEndThreshold'] = $machineDetectionSpeechEndThreshold;
        $this->options['machineDetectionSilenceTimeout'] = $machineDetectionSilenceTimeout;
        $this->options['amdStatusCallback'] = $amdStatusCallback;
        $this->options['amdStatusCallbackMethod'] = $amdStatusCallbackMethod;
    }

    /**
     * The URL we should call using the `status_callback_method` to send status information to your application.
     *
     * @param string $statusCallback The URL we should call to send status
     *                               information to your application
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
     *
     * @param string $statusCallbackMethod The HTTP method we should use to call
     *                                     `status_callback`
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
     *
     * @param string[] $statusCallbackEvent Set state change events that will
     *                                      trigger a callback
     * @return $this Fluent Builder
     */
    public function setStatusCallbackEvent(array $statusCallbackEvent): self {
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        return $this;
    }

    /**
     * A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
     *
     * @param string $label The label of this participant
     * @return $this Fluent Builder
     */
    public function setLabel(string $label): self {
        $this->options['label'] = $label;
        return $this;
    }

    /**
     * The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
     *
     * @param int $timeout he number of seconds that we should wait for an answer
     * @return $this Fluent Builder
     */
    public function setTimeout(int $timeout): self {
        $this->options['timeout'] = $timeout;
        return $this;
    }

    /**
     * Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     *
     * @param bool $record Whether to record the participant and their conferences
     * @return $this Fluent Builder
     */
    public function setRecord(bool $record): self {
        $this->options['record'] = $record;
        return $this;
    }

    /**
     * Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
     *
     * @param bool $muted Whether to mute the agent
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     *
     * @param string $beep Whether to play a notification beep to the conference
     *                     when the participant joins
     * @return $this Fluent Builder
     */
    public function setBeep(string $beep): self {
        $this->options['beep'] = $beep;
        return $this;
    }

    /**
     * Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     *
     * @param bool $startConferenceOnEnter Whether the conference starts when the
     *                                     participant joins the conference
     * @return $this Fluent Builder
     */
    public function setStartConferenceOnEnter(bool $startConferenceOnEnter): self {
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        return $this;
    }

    /**
     * Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     *
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  participant leaves
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnExit(bool $endConferenceOnExit): self {
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        return $this;
    }

    /**
     * The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     *
     * @param string $waitUrl URL that hosts pre-conference hold music
     * @return $this Fluent Builder
     */
    public function setWaitUrl(string $waitUrl): self {
        $this->options['waitUrl'] = $waitUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     *
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @return $this Fluent Builder
     */
    public function setWaitMethod(string $waitMethod): self {
        $this->options['waitMethod'] = $waitMethod;
        return $this;
    }

    /**
     * Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
     *
     * @param bool $earlyMedia Whether agents can hear the state of the outbound
     *                         call
     * @return $this Fluent Builder
     */
    public function setEarlyMedia(bool $earlyMedia): self {
        $this->options['earlyMedia'] = $earlyMedia;
        return $this;
    }

    /**
     * The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     *
     * @param int $maxParticipants The maximum number of agent conference
     *                             participants
     * @return $this Fluent Builder
     */
    public function setMaxParticipants(int $maxParticipants): self {
        $this->options['maxParticipants'] = $maxParticipants;
        return $this;
    }

    /**
     * Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     *
     * @param string $conferenceRecord Whether to record the conference the
     *                                 participant is joining
     * @return $this Fluent Builder
     */
    public function setConferenceRecord(string $conferenceRecord): self {
        $this->options['conferenceRecord'] = $conferenceRecord;
        return $this;
    }

    /**
     * Whether to trim leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     *
     * @param string $conferenceTrim Whether to trim leading and trailing silence
     *                               from your recorded conference audio files
     * @return $this Fluent Builder
     */
    public function setConferenceTrim(string $conferenceTrim): self {
        $this->options['conferenceTrim'] = $conferenceTrim;
        return $this;
    }

    /**
     * The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     *
     * @param string $conferenceStatusCallback The callback URL for conference
     *                                         events
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallback(string $conferenceStatusCallback): self {
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceStatusCallbackMethod HTTP method for requesting
     *                                               `conference_status_callback`
     *                                               URL
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackMethod(string $conferenceStatusCallbackMethod): self {
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        return $this;
    }

    /**
     * The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
     *
     * @param string[] $conferenceStatusCallbackEvent The conference state changes
     *                                                that should generate a call
     *                                                to
     *                                                `conference_status_callback`
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackEvent(array $conferenceStatusCallbackEvent): self {
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        return $this;
    }

    /**
     * The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     *
     * @param string $recordingChannels Specify `mono` or `dual` recording channels
     * @return $this Fluent Builder
     */
    public function setRecordingChannels(string $recordingChannels): self {
        $this->options['recordingChannels'] = $recordingChannels;
        return $this;
    }

    /**
     * The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     *
     * @param string $recordingStatusCallback The URL that we should call using the
     *                                        `recording_status_callback_method`
     *                                        when the recording status changes
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallback(string $recordingStatusCallback): self {
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $recordingStatusCallbackMethod The HTTP method we should use
     *                                              when we call
     *                                              `recording_status_callback`
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallbackMethod(string $recordingStatusCallbackMethod): self {
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The SIP username used for authentication.
     *
     * @param string $sipAuthUsername The SIP username used for authentication
     * @return $this Fluent Builder
     */
    public function setSipAuthUsername(string $sipAuthUsername): self {
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        return $this;
    }

    /**
     * The SIP password for authentication.
     *
     * @param string $sipAuthPassword The SIP password for authentication
     * @return $this Fluent Builder
     */
    public function setSipAuthPassword(string $sipAuthPassword): self {
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        return $this;
    }

    /**
     * The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     *
     * @param string $region The region where we should mix the conference audio
     * @return $this Fluent Builder
     */
    public function setRegion(string $region): self {
        $this->options['region'] = $region;
        return $this;
    }

    /**
     * The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     *
     * @param string $conferenceRecordingStatusCallback The URL we should call
     *                                                  using the
     *                                                  `conference_recording_status_callback_method` when the conference recording is available
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallback(string $conferenceRecordingStatusCallback): self {
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we
     *                                                        should use to call
     *                                                        `conference_recording_status_callback`
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallbackMethod(string $conferenceRecordingStatusCallbackMethod): self {
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
     *
     * @param string[] $recordingStatusCallbackEvent The recording state changes
     *                                               that should generate a call to
     *                                               `recording_status_callback`
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallbackEvent(array $recordingStatusCallbackEvent): self {
        $this->options['recordingStatusCallbackEvent'] = $recordingStatusCallbackEvent;
        return $this;
    }

    /**
     * The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
     *
     * @param string[] $conferenceRecordingStatusCallbackEvent The conference
     *                                                         recording state
     *                                                         changes that should
     *                                                         generate a call to
     *                                                         `conference_recording_status_callback`
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallbackEvent(array $conferenceRecordingStatusCallbackEvent): self {
        $this->options['conferenceRecordingStatusCallbackEvent'] = $conferenceRecordingStatusCallbackEvent;
        return $this;
    }

    /**
     * Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     *
     * @param bool $coaching Indicates if the participant changed to coach
     * @return $this Fluent Builder
     */
    public function setCoaching(bool $coaching): self {
        $this->options['coaching'] = $coaching;
        return $this;
    }

    /**
     * The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     *
     * @param string $callSidToCoach The SID of the participant who is being
     *                               `coached`
     * @return $this Fluent Builder
     */
    public function setCallSidToCoach(string $callSidToCoach): self {
        $this->options['callSidToCoach'] = $callSidToCoach;
        return $this;
    }

    /**
     * Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
     *
     * @param string $jitterBufferSize Jitter Buffer size for the connecting
     *                                 participant
     * @return $this Fluent Builder
     */
    public function setJitterBufferSize(string $jitterBufferSize): self {
        $this->options['jitterBufferSize'] = $jitterBufferSize;
        return $this;
    }

    /**
     * The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
     *
     * @param string $byoc BYOC trunk SID (Beta)
     * @return $this Fluent Builder
     */
    public function setByoc(string $byoc): self {
        $this->options['byoc'] = $byoc;
        return $this;
    }

    /**
     * The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
     *
     * @param string $callerId The phone number, Client identifier, or username
     *                         portion of SIP address that made this call.
     * @return $this Fluent Builder
     */
    public function setCallerId(string $callerId): self {
        $this->options['callerId'] = $callerId;
        return $this;
    }

    /**
     * The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
     *
     * @param string $callReason Reason for the call (Branded Calls Beta)
     * @return $this Fluent Builder
     */
    public function setCallReason(string $callReason): self {
        $this->options['callReason'] = $callReason;
        return $this;
    }

    /**
     * The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
     *
     * @param string $recordingTrack The track(s) to record
     * @return $this Fluent Builder
     */
    public function setRecordingTrack(string $recordingTrack): self {
        $this->options['recordingTrack'] = $recordingTrack;
        return $this;
    }

    /**
     * The maximum duration of the call in seconds. Constraints depend on account and configuration.
     *
     * @param int $timeLimit The maximum duration of the call in seconds.
     * @return $this Fluent Builder
     */
    public function setTimeLimit(int $timeLimit): self {
        $this->options['timeLimit'] = $timeLimit;
        return $this;
    }

    /**
     * Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. If `send_digits` is provided, this parameter is ignored. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
     *
     * @param string $machineDetection Enable machine detection or end of greeting
     *                                 detection
     * @return $this Fluent Builder
     */
    public function setMachineDetection(string $machineDetection): self {
        $this->options['machineDetection'] = $machineDetection;
        return $this;
    }

    /**
     * The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
     *
     * @param int $machineDetectionTimeout Number of seconds to wait for machine
     *                                     detection
     * @return $this Fluent Builder
     */
    public function setMachineDetectionTimeout(int $machineDetectionTimeout): self {
        $this->options['machineDetectionTimeout'] = $machineDetectionTimeout;
        return $this;
    }

    /**
     * The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
     *
     * @param int $machineDetectionSpeechThreshold Number of milliseconds for
     *                                             measuring stick for the length
     *                                             of the speech activity
     * @return $this Fluent Builder
     */
    public function setMachineDetectionSpeechThreshold(int $machineDetectionSpeechThreshold): self {
        $this->options['machineDetectionSpeechThreshold'] = $machineDetectionSpeechThreshold;
        return $this;
    }

    /**
     * The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
     *
     * @param int $machineDetectionSpeechEndThreshold Number of milliseconds of
     *                                                silence after speech activity
     * @return $this Fluent Builder
     */
    public function setMachineDetectionSpeechEndThreshold(int $machineDetectionSpeechEndThreshold): self {
        $this->options['machineDetectionSpeechEndThreshold'] = $machineDetectionSpeechEndThreshold;
        return $this;
    }

    /**
     * The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
     *
     * @param int $machineDetectionSilenceTimeout Number of milliseconds of initial
     *                                            silence
     * @return $this Fluent Builder
     */
    public function setMachineDetectionSilenceTimeout(int $machineDetectionSilenceTimeout): self {
        $this->options['machineDetectionSilenceTimeout'] = $machineDetectionSilenceTimeout;
        return $this;
    }

    /**
     * The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
     *
     * @param string $amdStatusCallback The URL we should call to send amd status
     *                                  information to your application
     * @return $this Fluent Builder
     */
    public function setAmdStatusCallback(string $amdStatusCallback): self {
        $this->options['amdStatusCallback'] = $amdStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
     *
     * @param string $amdStatusCallbackMethod HTTP Method to use with
     *                                        amd_status_callback
     * @return $this Fluent Builder
     */
    public function setAmdStatusCallbackMethod(string $amdStatusCallbackMethod): self {
        $this->options['amdStatusCallbackMethod'] = $amdStatusCallbackMethod;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateParticipantOptions ' . $options . ']';
    }
}

class ReadParticipantOptions extends Options {
    /**
     * @param bool $muted Whether to return only participants that are muted
     * @param bool $hold Whether to return only participants that are on hold
     * @param bool $coaching Whether to return only participants who are coaching
     *                       another call
     */
    public function __construct(bool $muted = Values::NONE, bool $hold = Values::NONE, bool $coaching = Values::NONE) {
        $this->options['muted'] = $muted;
        $this->options['hold'] = $hold;
        $this->options['coaching'] = $coaching;
    }

    /**
     * Whether to return only participants that are muted. Can be: `true` or `false`.
     *
     * @param bool $muted Whether to return only participants that are muted
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether to return only participants that are on hold. Can be: `true` or `false`.
     *
     * @param bool $hold Whether to return only participants that are on hold
     * @return $this Fluent Builder
     */
    public function setHold(bool $hold): self {
        $this->options['hold'] = $hold;
        return $this;
    }

    /**
     * Whether to return only participants who are coaching another call. Can be: `true` or `false`.
     *
     * @param bool $coaching Whether to return only participants who are coaching
     *                       another call
     * @return $this Fluent Builder
     */
    public function setCoaching(bool $coaching): self {
        $this->options['coaching'] = $coaching;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.ReadParticipantOptions ' . $options . ']';
    }
}