<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Accounts;

use Twi<PERSON>\Domain;
use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use T<PERSON><PERSON>\InstanceContext;
use <PERSON><PERSON><PERSON>\Rest\Accounts\V1\AuthTokenPromotionList;
use Twilio\Rest\Accounts\V1\CredentialList;
use Twilio\Rest\Accounts\V1\SecondaryAuthTokenList;
use Twilio\Version;

/**
 * @property AuthTokenPromotionList $authTokenPromotion
 * @property CredentialList $credentials
 * @property SecondaryAuthTokenList $secondaryAuthToken
 */
class V1 extends Version {
    protected $_authTokenPromotion;
    protected $_credentials;
    protected $_secondaryAuthToken;

    /**
     * Construct the V1 version of Accounts
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain) {
        parent::__construct($domain);
        $this->version = 'v1';
    }

    protected function getAuthTokenPromotion(): AuthTokenPromotionList {
        if (!$this->_authTokenPromotion) {
            $this->_authTokenPromotion = new AuthTokenPromotionList($this);
        }
        return $this->_authTokenPromotion;
    }

    protected function getCredentials(): CredentialList {
        if (!$this->_credentials) {
            $this->_credentials = new CredentialList($this);
        }
        return $this->_credentials;
    }

    protected function getSecondaryAuthToken(): SecondaryAuthTokenList {
        if (!$this->_secondaryAuthToken) {
            $this->_secondaryAuthToken = new SecondaryAuthTokenList($this);
        }
        return $this->_secondaryAuthToken;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name) {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Accounts.V1]';
    }
}