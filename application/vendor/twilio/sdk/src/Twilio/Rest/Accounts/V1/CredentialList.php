<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Accounts\V1;

use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\InstanceContext;
use <PERSON><PERSON><PERSON>\ListResource;
use Twilio\Rest\Accounts\V1\Credential\AwsList;
use Twilio\Rest\Accounts\V1\Credential\PublicKeyList;
use Twilio\Version;

/**
 * @property PublicKeyList $publicKey
 * @property AwsList $aws
 * @method \Twilio\Rest\Accounts\V1\Credential\PublicKeyContext publicKey(string $sid)
 * @method \Twilio\Rest\Accounts\V1\Credential\AwsContext aws(string $sid)
 */
class CredentialList extends ListResource {
    protected $_publicKey = null;
    protected $_aws = null;

    /**
     * Construct the CredentialList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(Version $version) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [];
    }

    /**
     * Access the publicKey
     */
    protected function getPublicKey(): PublicKeyList {
        if (!$this->_publicKey) {
            $this->_publicKey = new PublicKeyList($this->version);
        }

        return $this->_publicKey;
    }

    /**
     * Access the aws
     */
    protected function getAws(): AwsList {
        if (!$this->_aws) {
            $this->_aws = new AwsList($this->version);
        }

        return $this->_aws;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return \Twilio\ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name) {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Accounts.V1.CredentialList]';
    }
}