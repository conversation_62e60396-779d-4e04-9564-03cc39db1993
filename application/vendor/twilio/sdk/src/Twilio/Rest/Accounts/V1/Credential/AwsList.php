<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Accounts\V1\Credential;

use T<PERSON>lio\Exceptions\TwilioException;
use T<PERSON>lio\ListResource;
use Twilio\Options;
use Twilio\Stream;
use Twilio\Values;
use Twilio\Version;

class AwsList extends ListResource {
    /**
     * Construct the AwsList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(Version $version) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [];

        $this->uri = '/Credentials/AWS';
    }

    /**
     * Streams AwsInstance records from the API as a generator stream.
     * This operation lazily loads records as efficiently as possible until the
     * limit
     * is reached.
     * The results are returned as a generator, so this operation is memory
     * efficient.
     *
     * @param int $limit Upper limit for the number of records to return. stream()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, stream()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return Stream stream of results
     */
    public function stream(int $limit = null, $pageSize = null): Stream {
        $limits = $this->version->readLimits($limit, $pageSize);

        $page = $this->page($limits['pageSize']);

        return $this->version->stream($page, $limits['limit'], $limits['pageLimit']);
    }

    /**
     * Reads AwsInstance records from the API as a list.
     * Unlike stream(), this operation is eager and will load `limit` records into
     * memory before returning.
     *
     * @param int $limit Upper limit for the number of records to return. read()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, read()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return AwsInstance[] Array of results
     */
    public function read(int $limit = null, $pageSize = null): array {
        return \iterator_to_array($this->stream($limit, $pageSize), false);
    }

    /**
     * Retrieve a single page of AwsInstance records from the API.
     * Request is executed immediately
     *
     * @param mixed $pageSize Number of records to return, defaults to 50
     * @param string $pageToken PageToken provided by the API
     * @param mixed $pageNumber Page Number, this value is simply for client state
     * @return AwsPage Page of AwsInstance
     */
    public function page($pageSize = Values::NONE, string $pageToken = Values::NONE, $pageNumber = Values::NONE): AwsPage {
        $params = Values::of(['PageToken' => $pageToken, 'Page' => $pageNumber, 'PageSize' => $pageSize, ]);

        $response = $this->version->page('GET', $this->uri, $params);

        return new AwsPage($this->version, $response, $this->solution);
    }

    /**
     * Retrieve a specific page of AwsInstance records from the API.
     * Request is executed immediately
     *
     * @param string $targetUrl API-generated URL for the requested results page
     * @return AwsPage Page of AwsInstance
     */
    public function getPage(string $targetUrl): AwsPage {
        $response = $this->version->getDomain()->getClient()->request(
            'GET',
            $targetUrl
        );

        return new AwsPage($this->version, $response, $this->solution);
    }

    /**
     * Create the AwsInstance
     *
     * @param string $credentials A string that contains the AWS access credentials
     *                            in the format
     *                            <AWS_ACCESS_KEY_ID>:<AWS_SECRET_ACCESS_KEY>
     * @param array|Options $options Optional Arguments
     * @return AwsInstance Created AwsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $credentials, array $options = []): AwsInstance {
        $options = new Values($options);

        $data = Values::of([
            'Credentials' => $credentials,
            'FriendlyName' => $options['friendlyName'],
            'AccountSid' => $options['accountSid'],
        ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new AwsInstance($this->version, $payload);
    }

    /**
     * Constructs a AwsContext
     *
     * @param string $sid The unique string that identifies the resource
     */
    public function getContext(string $sid): AwsContext {
        return new AwsContext($this->version, $sid);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Accounts.V1.AwsList]';
    }
}