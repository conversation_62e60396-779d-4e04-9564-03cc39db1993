<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Check if tax compliance mode is enabled
 *
 * @return boolean
 */
function is_tax_compliance_mode_enabled()
{
    // To disable tax compliance mode from the database, run:
    // UPDATE `tblsettings` SET `value` = '0' WHERE `name` = 'tax_compliance_mode';
    return get_option('tax_compliance_mode') == '1';
}

/**
 * Check if the current action is allowed in tax compliance mode
 * 
 * @param string $action The action to check (edit_invoice, delete_invoice, merge_invoice, edit_credit_note, delete_credit_note)
 * @param mixed $item The item to check (optional)
 * @return boolean
 */
function is_action_allowed_in_tax_compliance_mode($action, $item = null)
{
    if (!is_tax_compliance_mode_enabled()) {
        return true;
    }

    // All actions are restricted in tax compliance mode except for specific exceptions
    switch ($action) {
        case 'edit_invoice':
            // Allow editing draft invoices (not finalized yet)
            if (isset($item) && is_object($item) && isset($item->status)) {
                // Draft invoices can be edited
                if ($item->status == 6) { // STATUS_DRAFT
                    return true;
                }
                // Recurring invoices can be edited
                if (isset($item->recurring) && $item->recurring > 0) {
                    return true;
                }
            }
            return false;

        case 'cancel_invoice':
            // Allow canceling draft invoices and recurring invoices
            if (isset($item) && is_object($item) && isset($item->status)) {
                // Draft invoices can be cancelled
                if ($item->status == 6) { // STATUS_DRAFT
                    return true;
                }
                // Recurring invoices can be cancelled
                if (isset($item->recurring) && $item->recurring > 0) {
                    return true;
                }
            }
            return false;

        case 'delete_invoice':
            // Allow deleting only draft invoices
            if (isset($item) && is_object($item) && isset($item->status)) {
                // Draft invoices can be deleted
                if ($item->status == 6) { // STATUS_DRAFT
                    return true;
                }
            }
            return false;

        case 'merge_invoice':
        case 'edit_credit_note':
        case 'delete_credit_note':
        case 'delete_refund':
        case 'edit_refund':
        case 'edit_invoice_number':
        case 'edit_credit_note_number':
            // These actions are always restricted in tax compliance mode
            return false;

        default:
            // If we don't explicitly handle an action, restrict it to be safe
            return false;
    }
}

/**
 * Check if a specific invoice field can be edited in tax compliance mode
 *
 * @param string $field_name The field name to check
 * @param object $invoice Invoice object for context
 * @return boolean
 */
function is_invoice_field_editable_in_tax_compliance_mode($field_name, $invoice = null)
{
    // If tax compliance mode is not enabled, all fields are editable
    if (!is_tax_compliance_mode_enabled()) {
        return true;
    }

    // If no invoice provided, restrict all fields
    if (!$invoice) {
        return false;
    }

    // Draft invoices are fully editable
    if (isset($invoice->status) && $invoice->status == 6) { // STATUS_DRAFT
        return true;
    }

    // For finalized invoices, only specific fields are editable
    $editable_fields_for_finalized = [
        'payment_modes',
        'sale_agent',
        'adminnote',
        'tags',
        'admin_note'
    ];

    return in_array($field_name, $editable_fields_for_finalized);
}

/**
 * Check if invoice line items can be modified in tax compliance mode
 *
 * @param object $invoice Invoice object for context
 * @return boolean
 */
function can_modify_invoice_items_in_tax_compliance_mode($invoice = null)
{
    // If tax compliance mode is not enabled, items can be modified
    if (!is_tax_compliance_mode_enabled()) {
        return true;
    }

    // If no invoice provided, restrict modifications
    if (!$invoice) {
        return false;
    }

    // Only draft invoices can have items modified
    return isset($invoice->status) && $invoice->status == 6; // STATUS_DRAFT
}

/**
 * Add a warning message for tax compliance mode
 * 
 * @param string $context The context for the warning (enabling, invoice, credit_note)
 * @return string HTML for the warning message
 */
function get_tax_compliance_warning($context = 'enabling')
{
    $CI = &get_instance();
    
    switch ($context) {
        case 'invoice':
            return '<div class="alert alert-warning" style="font-size: 14px; border-left: 5px solid #f39c12;">
                <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> <strong>Tax Compliance Mode Active</strong></h4>
                <p>This invoice is subject to tax compliance regulations. The following restrictions apply:</p>
                <ul>
                    <li>Draft invoices remain fully editable and deletable</li>
                    <li>Finalized invoices have restricted editing (only payment methods, sales agent, admin notes, and tags)</li>
                    <li>Recurring invoices can be managed (cancel/generate recurrence)</li>
                    <li>Changing invoice numbers or prefixes is not allowed</li>
                    <li>Merging invoices is disabled</li>
                    <li>Only draft invoices can be deleted</li>
                </ul>
                <p>These restrictions are in place to ensure compliance with tax regulations.</p>
            </div>';
            
        case 'credit_note':
            return '<div class="alert alert-warning" style="font-size: 14px; border-left: 5px solid #f39c12;">
                <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> <strong>Tax Compliance Mode Active</strong></h4>
                <p>This credit note is subject to tax compliance regulations. The following restrictions apply:</p>
                <ul>
                    <li>Editing credit notes is not allowed</li>
                    <li>Changing credit note numbers or prefixes is not allowed</li>
                    <li>Credit notes are restricted to returns only</li>
                    <li>Deleting credit notes is disabled</li>
                </ul>
                <p>These restrictions are in place to ensure compliance with tax regulations.</p>
            </div>';
            
        case 'enabling':
        default:
            return '<div class="alert alert-warning" style="font-size: 14px; border-left: 5px solid #f39c12;">
                <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> <strong>Warning: This decision is irreversible from the admin panel!</strong></h4>
                <p>This decision is irreversible from the admin panel, and can only be disabled by directly modifying the database.</p>
                <p>The following restrictions will be applied when tax compliance mode is enabled:</p>
                <ul>
                    <li>Draft invoices remain fully editable and deletable</li>
                    <li>Finalized invoices have restricted editing (only payment methods, sales agent, admin notes, and tags)</li>
                    <li>Recurring invoices can be managed (cancel/generate recurrence)</li>
                    <li>The ability to control invoice numbering or prefixes is locked.</li>
                    <li>The option to merge invoices is disabled.</li>
                    <li>Only draft invoices can be deleted.</li>
                    <li>The ability to edit credit notes is locked in all areas where credit note editing is possible.</li>
                    <li>The ability to control credit note numbering or prefixes is locked.</li>
                    <li>Credit notes are restricted to returns only.</li>
                    <li>The option to delete credit notes is disabled.</li>
                </ul>
                <p><strong>Are you sure you want to enable tax compliance mode?</strong></p>
            </div>';
    }
} 