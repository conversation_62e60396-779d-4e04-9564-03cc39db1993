<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Tax Compliance Secure Deactivation Modal -->
<div class="modal fade" id="taxComplianceDeactivationModal" tabindex="-1" role="dialog" aria-labelledby="taxComplianceDeactivationModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-dialog-centered tax-compliance-modal-container" role="document">
        <div class="modal-content tax-compliance-modal-modern">

            <!-- Modal Header -->
            <div class="modal-header tax-compliance-header-modern">
                <div class="header-wrapper">
                    <div class="header-icon-wrapper">
                        <div class="header-icon-modern">
                            <i class="fa fa-shield"></i>
                        </div>
                    </div>
                    <div class="header-content-modern">
                        <h4 class="modal-title-modern" id="taxComplianceDeactivationModalLabel">
                            <?= _l('tax_compliance_secure_deactivation'); ?>
                        </h4>
                        <p class="header-subtitle-modern">
                            <?= _l('tax_compliance_email_verification_required'); ?>
                        </p>
                    </div>
                    <button type="button" class="close-btn-modern" data-dismiss="modal" aria-label="<?= _l('close'); ?>">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="modal-body tax-compliance-body-modern">

                <!-- Step 1: Request OTP -->
                <div id="step-request-otp" class="step-container step-active">
                    <div class="step-content">
                        <div class="step-icon-container">
                            <div class="step-icon step-icon-primary">
                                <i class="fa fa-envelope"></i>
                            </div>
                        </div>

                        <div class="step-info">
                            <h3 class="step-title"><?= _l('tax_compliance_request_verification'); ?></h3>
                            <p class="step-description"><?= _l('tax_compliance_otp_explanation'); ?></p>
                        </div>

                        <div class="email-info-card">
                            <div class="email-icon">
                                <i class="fa fa-envelope-o"></i>
                            </div>
                            <div class="email-details">
                                <span class="email-label"><?= _l('tax_compliance_verification_email'); ?></span>
                                <span id="admin-email-display" class="email-value">
                                    <?= get_staff_user_id() ? get_staff(get_staff_user_id())->email : ''; ?>
                                </span>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" id="request-otp-btn" class="btn-modern btn-primary">
                                <i class="fa fa-paper-plane"></i>
                                <span><?= _l('tax_compliance_send_verification_code'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Verify OTP -->
                <div id="step-verify-otp" class="step-container" style="display: none;">
                    <div class="step-content">
                        <div class="step-icon-container">
                            <div class="step-icon step-icon-success">
                                <i class="fa fa-key"></i>
                            </div>
                        </div>

                        <div class="step-info">
                            <h3 class="step-title"><?= _l('tax_compliance_enter_verification_code'); ?></h3>
                            <p class="step-description"><?= _l('tax_compliance_check_email_for_code'); ?></p>
                        </div>

                        <div class="otp-section">
                            <div class="otp-input-wrapper">
                                <label class="otp-label"><?= _l('tax_compliance_verification_code'); ?></label>
                                <input type="text" id="otp-input" maxlength="6" placeholder="000000"
                                       class="otp-input-modern" autocomplete="off">
                            </div>

                            <div class="otp-status">
                                <div id="otp-timer" class="timer-display"></div>
                                <div id="attempts-remaining" class="attempts-display"></div>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" id="verify-otp-btn" class="btn-modern btn-success">
                                <i class="fa fa-check"></i>
                                <span><?= _l('tax_compliance_verify_and_deactivate'); ?></span>
                            </button>
                            <button type="button" id="resend-otp-btn" class="btn-modern btn-secondary">
                                <i class="fa fa-refresh"></i>
                                <span><?= _l('tax_compliance_resend_code'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Success -->
                <div id="step-success" class="step-container" style="display: none;">
                    <div class="step-content success-content">
                        <div class="step-icon-container">
                            <div class="step-icon step-icon-complete">
                                <i class="fa fa-check"></i>
                            </div>
                        </div>

                        <div class="step-info">
                            <h3 class="step-title success-title"><?= _l('tax_compliance_deactivation_successful'); ?></h3>
                            <p class="step-description success-message"><?= _l('tax_compliance_module_deactivated_message'); ?></p>
                        </div>

                        <div class="step-actions">
                            <button type="button" id="close-success-btn" class="btn-modern btn-primary">
                                <i class="fa fa-check-circle"></i>
                                <span><?= _l('close'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Modern Clean UI/UX Styles -->
<style>
/* Modal Container */
.tax-compliance-modal-container {
    max-width: 480px;
    margin: 0 auto;
}

.tax-compliance-modal-modern {
    border: none;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    animation: modalFadeIn 0.3s ease-out;
    background: #ffffff;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Header */
.tax-compliance-header-modern {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    padding: 0;
    position: relative;
    overflow: hidden;
}

.tax-compliance-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.header-wrapper {
    display: flex;
    align-items: center;
    padding: 24px 28px;
    position: relative;
    z-index: 1;
}

.header-icon-wrapper {
    margin-right: 16px;
}

.header-icon-modern {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-icon-modern i {
    color: white;
    font-size: 20px;
}

.header-content-modern {
    flex: 1;
}

.modal-title-modern {
    color: white;
    margin: 0 0 4px 0;
    font-weight: 600;
    font-size: 18px;
    line-height: 1.3;
}

.header-subtitle-modern {
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
    font-size: 13px;
    font-weight: 400;
}

.close-btn-modern {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    border-radius: 12px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.close-btn-modern:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

/* Body */
.tax-compliance-body-modern {
    padding: 32px 28px;
    background: #ffffff;
    min-height: 320px;
    display: flex;
    flex-direction: column;
}

/* Step Container */
.step-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    animation: stepFadeIn 0.4s ease-out;
}

@keyframes stepFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

/* Step Icon */
.step-icon-container {
    margin-bottom: 20px;
}

.step-icon {
    width: 64px;
    height: 64px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.step-icon i {
    font-size: 24px;
    position: relative;
    z-index: 1;
}

.step-icon-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.step-icon-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.step-icon-complete {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
}

/* Step Info */
.step-info {
    margin-bottom: 24px;
    flex: 1;
}

.step-title {
    color: #1f2937;
    margin: 0 0 8px 0;
    font-weight: 600;
    font-size: 20px;
    line-height: 1.3;
}

.step-description {
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
    font-size: 14px;
}

/* Email Info Card */
.email-info-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 20px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 360px;
}

.email-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
}

.email-icon i {
    color: white;
    font-size: 16px;
}

.email-details {
    flex: 1;
    text-align: left;
}

.email-label {
    display: block;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.email-value {
    color: #1f2937;
    font-size: 14px;
    font-weight: 600;
    word-break: break-all;
}

/* OTP Section */
.otp-section {
    margin: 24px 0;
    width: 100%;
    max-width: 280px;
}

.otp-input-wrapper {
    margin-bottom: 16px;
}

.otp-label {
    display: block;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    text-align: left;
}

.otp-input-modern {
    width: 100%;
    height: 56px;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 8px;
    background: #ffffff;
    transition: all 0.2s ease;
    outline: none;
}

.otp-input-modern:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.otp-input-modern::placeholder {
    color: #d1d5db;
    letter-spacing: 4px;
}

.otp-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.timer-display {
    color: #f59e0b;
    font-weight: 500;
}

.attempts-display {
    color: #6b7280;
}
}

/* Step Actions */
.step-actions {
    margin-top: auto;
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
}

/* Modern Buttons */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 24px;
    border: none;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
    position: relative;
    overflow: hidden;
}

.btn-modern:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover:before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
    background: #f8fafc;
    color: #64748b;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.btn-modern:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-modern i {
    font-size: 14px;
}

/* Success Content */
.success-content {
    text-align: center;
}

.success-title {
    color: #059669;
    margin-bottom: 8px;
}

.success-message {
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 576px) {
    .tax-compliance-modal-container {
        max-width: 95%;
        margin: 10px auto;
    }

    .tax-compliance-body-modern {
        padding: 24px 20px;
        min-height: 280px;
    }

    .header-wrapper {
        padding: 20px 24px;
    }

    .modal-title-modern {
        font-size: 16px;
    }

    .header-subtitle-modern {
        font-size: 12px;
    }

    .step-icon {
        width: 56px;
        height: 56px;
    }

    .step-icon i {
        font-size: 20px;
    }

    .step-title {
        font-size: 18px;
    }

    .step-description {
        font-size: 13px;
    }

    .email-info-card {
        padding: 16px;
        max-width: 100%;
    }

    .email-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
    }

    .email-icon i {
        font-size: 14px;
    }

    .otp-section {
        max-width: 100%;
    }

    .otp-input-modern {
        height: 48px;
        font-size: 16px;
        letter-spacing: 6px;
    }

    .btn-modern {
        min-width: 120px;
        padding: 12px 20px;
        font-size: 13px;
    }

    .step-actions {
        flex-direction: column;
        gap: 8px;
    }
}

@media (max-width: 400px) {
    .tax-compliance-modal-container {
        max-width: 100%;
        margin: 5px;
    }

    .header-wrapper {
        padding: 16px 20px;
    }

    .tax-compliance-body-modern {
        padding: 20px 16px;
    }

    .email-info-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .email-icon {
        margin-right: 0;
    }

    .otp-input-modern {
        letter-spacing: 4px;
    }
}

/* Animation Classes */
.step-active {
    animation: stepFadeIn 0.4s ease-out;
}

/* Loading States */
.btn-modern.loading {
    pointer-events: none;
}

.btn-modern.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Focus States */
.btn-modern:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .tax-compliance-modal-modern {
        background: #1f2937;
    }

    .tax-compliance-body-modern {
        background: #1f2937;
    }

    .step-title {
        color: #f9fafb;
    }

    .step-description {
        color: #d1d5db;
    }

    .email-info-card {
        background: #374151;
        border-color: #4b5563;
    }

    .email-label {
        color: #d1d5db;
    }

    .email-value {
        color: #f9fafb;
    }

    .otp-input-modern {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .otp-input-modern:focus {
        border-color: #3b82f6;
        background: #1f2937;
    }
}

}
</style>
