<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Tax Compliance Mode Section - Enhanced UI/UX - Arabic Version -->
<div class="tax-compliance-section" style="margin: 30px 0; direction: rtl; text-align: right;">
    <div class="panel panel-default" style="border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border-radius: 12px; overflow: hidden;">
        <!-- Header with gradient background -->
        <div class="panel-heading" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; padding: 25px 30px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                    <div style="background: rgba(255,255,255,0.2); border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                        <i class="fa fa-shield" style="color: white; font-size: 22px;"></i>
                    </div>
                    <div>
                        <h3 style="color: white; margin: 0; font-weight: 600; font-size: 24px;">وضع الامتثال الضريبي</h3>
                        <p style="color: rgba(255,255,255,0.9); margin: 5px 0 0 0; font-size: 14px;">حماية متقدمة للفواتير والإشعارات الائتمانية</p>
                    </div>
                </div>
                <?php if(get_option('tax_compliance_mode') == '1'): ?>
                <div style="background: rgba(76, 175, 80, 0.9); color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">
                    <i class="fa fa-check-circle" style="margin-left: 5px;"></i>مفعل
                </div>
                <?php else: ?>
                <div style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">
                    <i class="fa fa-circle-o" style="margin-left: 5px;"></i>معطل
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="panel-body" style="padding: 30px;">
            <!-- Description with modern styling -->
            <div style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 10px; padding: 20px; margin-bottom: 25px; border-right: 4px solid #667eea;">
                <div style="display: flex; align-items: flex-start;">
                    <i class="fa fa-info-circle" style="color: #667eea; font-size: 20px; margin-left: 15px; margin-top: 2px;"></i>
                    <div>
                        <h5 style="color: #2c3e50; margin: 0 0 8px 0; font-weight: 600;">ما هو وضع الامتثال الضريبي؟</h5>
                        <p style="color: #5a6c7d; margin: 0; line-height: 1.6;">
                            ميزة أمان قوية تفرض ضوابط صارمة على الوثائق المالية لضمان الامتثال للوائح الضريبية والحفاظ على سلامة مسار المراجعة.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Modern Warning Dialog -->
            <div id="tax_compliance_warning" class="hide" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); border-radius: 12px; padding: 25px; margin-bottom: 25px; color: white; box-shadow: 0 8px 25px rgba(238, 90, 36, 0.3);">
                <div style="display: flex; align-items: flex-start; margin-bottom: 20px;">
                    <div style="background: rgba(255,255,255,0.2); border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin-left: 20px; flex-shrink: 0;">
                        <i class="fa fa-exclamation-triangle" style="color: white; font-size: 22px;"></i>
                    </div>
                    <div>
                        <h4 style="color: white; margin: 0 0 10px 0; font-weight: 600; font-size: 20px;">⚠️ قرار أمني حرج</h4>
                        <p style="color: rgba(255,255,255,0.95); margin: 0; font-size: 16px; line-height: 1.5;">
                            <strong>هذا الإجراء غير قابل للإلغاء من لوحة الإدارة!</strong><br>
                            بمجرد التفعيل، يمكن إلغاء وضع الامتثال الضريبي فقط عن طريق تعديل قاعدة البيانات مباشرة.
                        </p>
                    </div>
                </div>

                <!-- Restrictions List with Modern Design -->
                <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                    <h5 style="color: white; margin: 0 0 15px 0; font-weight: 600;">سيتم تطبيق القيود التالية:</h5>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 12px;">
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-check-circle" style="color: #4CAF50; margin-left: 10px;"></i>
                            <span style="color: rgba(255,255,255,0.9);">مسودات الفواتير تبقى قابلة للتحرير بالكامل</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-lock" style="color: #FFC107; margin-left: 10px;"></i>
                            <span style="color: rgba(255,255,255,0.9);">الفواتير المنجزة لها تحرير مقيد</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-refresh" style="color: #2196F3; margin-left: 10px;"></i>
                            <span style="color: rgba(255,255,255,0.9);">يمكن إدارة الفواتير المتكررة</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-ban" style="color: #f44336; margin-left: 10px;"></i>
                            <span style="color: rgba(255,255,255,0.9);">تغيير أرقام الفواتير محظور</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-ban" style="color: #f44336; margin-left: 10px;"></i>
                            <span style="color: rgba(255,255,255,0.9);">دمج الفواتير معطل</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-ban" style="color: #f44336; margin-left: 10px;"></i>
                            <span style="color: rgba(255,255,255,0.9);">تحرير الإشعارات الائتمانية محظور</span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <p style="color: white; margin-bottom: 20px; font-size: 18px; font-weight: 600;">هل أنت متأكد تماماً من رغبتك في تفعيل وضع الامتثال الضريبي؟</p>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button type="button" id="confirm_tax_compliance" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; font-weight: 600; font-size: 14px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);">
                            <i class="fa fa-check" style="margin-left: 8px;"></i>نعم، فعل الحماية
                        </button>
                        <button type="button" id="cancel_tax_compliance" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3); padding: 12px 30px; border-radius: 25px; font-weight: 600; font-size: 14px; cursor: pointer; transition: all 0.3s ease;">
                            <i class="fa fa-times" style="margin-left: 8px;"></i>إلغاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- Modern Toggle Section -->
            <div style="background: white; border-radius: 10px; padding: 25px; border: 2px solid #f1f3f4;">
                <?php if(get_option('tax_compliance_mode') == '1'): ?>
                    <!-- Active State -->
                    <div style="text-align: center;">
                        <div style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px auto; box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);">
                            <i class="fa fa-shield" style="color: white; font-size: 35px;"></i>
                        </div>
                        <h4 style="color: #2c3e50; margin: 0 0 10px 0; font-weight: 600;">وضع الامتثال الضريبي مفعل</h4>
                        <p style="color: #7f8c8d; margin: 0 0 20px 0;">وثائقك المالية محمية الآن بضوابط امتثال متقدمة.</p>

                        <!-- Status Information -->
                        <div style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%); border-radius: 10px; padding: 20px; margin-bottom: 20px; border-right: 4px solid #4CAF50;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; text-align: right;">
                                <?php if(get_option('tax_compliance_mode_activated_at')): ?>
                                <div style="display: flex; align-items: center;">
                                    <div>
                                        <strong style="color: #2c3e50; font-size: 14px;">تم التفعيل في</strong><br>
                                        <span style="color: #5a6c7d; font-size: 13px;"><?php echo date('j F Y، g:i a', strtotime(get_option('tax_compliance_mode_activated_at'))); ?></span>
                                    </div>
                                    <i class="fa fa-calendar" style="color: #4CAF50; margin-left: 10px; font-size: 16px;"></i>
                                </div>
                                <?php endif; ?>

                                <?php if(get_option('tax_compliance_mode_activated_by')):
                                    $CI =& get_instance();
                                    $CI->load->model('staff_model');
                                    $staff = $CI->staff_model->get(get_option('tax_compliance_mode_activated_by'));
                                    if($staff): ?>
                                <div style="display: flex; align-items: center;">
                                    <div>
                                        <strong style="color: #2c3e50; font-size: 14px;">تم التفعيل بواسطة</strong><br>
                                        <span style="color: #5a6c7d; font-size: 13px;"><?php echo $staff->firstname . ' ' . $staff->lastname; ?></span>
                                    </div>
                                    <i class="fa fa-user" style="color: #4CAF50; margin-left: 10px; font-size: 16px;"></i>
                                </div>
                                <?php endif; endif; ?>

                                <div style="display: flex; align-items: center;">
                                    <div>
                                        <strong style="color: #2c3e50; font-size: 14px;">إلغاء التفعيل</strong><br>
                                        <span style="color: #5a6c7d; font-size: 13px;">يتطلب تعديل قاعدة البيانات</span>
                                    </div>
                                    <i class="fa fa-database" style="color: #4CAF50; margin-left: 10px; font-size: 16px;"></i>
                                </div>
                            </div>
                        </div>

                        <div style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-radius: 8px; padding: 15px; border-right: 4px solid #ffc107;">
                            <div style="display: flex; align-items: center; justify-content: center;">
                                <span style="color: #856404; font-size: 14px; font-weight: 500;">يمكن إلغاء هذا الوضع فقط عن طريق تعديل قاعدة البيانات مباشرة</span>
                                <i class="fa fa-info-circle" style="color: #856404; margin-left: 10px;"></i>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Inactive State -->
                    <div style="text-align: center;">
                        <div style="background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px auto; box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);">
                            <i class="fa fa-shield" style="color: white; font-size: 35px;"></i>
                        </div>
                        <h4 style="color: #2c3e50; margin: 0 0 10px 0; font-weight: 600;">وضع الامتثال الضريبي معطل</h4>
                        <p style="color: #7f8c8d; margin: 0 0 25px 0;">فعل الحماية المتقدمة لوثائقك المالية لضمان الامتثال الضريبي.</p>

                        <!-- Toggle Switch with Modern Design -->
                        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; padding: 25px; margin-bottom: 20px;">
                            <div class="form-group" style="margin: 0;">
                                <label class="switch" style="position: relative; display: inline-block; width: 80px; height: 40px; margin: 0;">
                                    <input type="checkbox" name="settings[tax_compliance_mode]" id="tax_compliance_mode" value="1" style="opacity: 0; width: 0; height: 0;">
                                    <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); transition: .4s; border-radius: 40px; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);"></span>
                                    <span class="slider-text" style="position: absolute; color: white; font-size: 12px; font-weight: 600; top: 50%; left: 50%; transform: translate(-50%, -50%); pointer-events: none;">معطل</span>
                                </label>
                                <div style="margin-top: 15px;">
                                    <label for="tax_compliance_mode" style="color: #2c3e50; font-weight: 600; font-size: 16px; cursor: pointer;">تفعيل وضع الامتثال الضريبي</label>
                                </div>
                            </div>
                        </div>

                        <div style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border-radius: 8px; padding: 15px; border-right: 4px solid #17a2b8;">
                            <div style="display: flex; align-items: center; justify-content: center;">
                                <span style="color: #0c5460; font-size: 14px; font-weight: 500;">انقر على المفتاح أعلاه لتفعيل الحماية المتقدمة للوثائق المالية</span>
                                <i class="fa fa-lightbulb-o" style="color: #0c5460; margin-left: 10px;"></i>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for the toggle switch - Arabic Version -->
<style>
.switch input:checked + .slider {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4) !important;
}

.switch input:checked + .slider .slider-text {
    content: "مفعل";
}

.switch input:checked + .slider:before {
    transform: translateX(40px);
}

.slider:before {
    position: absolute;
    content: "";
    height: 32px;
    width: 32px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.switch input:checked + .slider .slider-text:after {
    content: "مفعل";
}

.switch input:not(:checked) + .slider .slider-text:after {
    content: "معطل";
}

/* Hover effects */
#confirm_tax_compliance:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6) !important;
}

#cancel_tax_compliance:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: translateY(-2px);
}

.tax-compliance-section .panel:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* RTL specific styles */
.tax-compliance-section {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tax-compliance-section * {
    direction: rtl;
    text-align: right;
}

.tax-compliance-section .fa {
    margin-left: 8px;
    margin-right: 0;
}
</style>

<script>
$(document).ready(function() {
    // Tax Compliance Mode with enhanced animations - Arabic Version
    $('#tax_compliance_mode').on('change', function() {
        if ($(this).is(':checked') && !$(this).is(':disabled')) {
            // Show warning dialog with animation
            $('#tax_compliance_warning').removeClass('hide').hide().fadeIn(400);
            // Uncheck the box until confirmed
            $(this).prop('checked', false);

            // Update slider text
            $('.slider-text').text('معطل');

            // Smooth scroll to the warning with offset
            $('html, body').animate({
                scrollTop: $('#tax_compliance_warning').offset().top - 120
            }, 800, 'easeInOutQuart');
        }
    });

    // Confirm tax compliance mode with enhanced UX - Arabic Version
    $('#confirm_tax_compliance').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();

        // Show loading state
        $btn.html('<i class="fa fa-spinner fa-spin" style="margin-left: 8px;"></i>جاري التفعيل...');
        $btn.prop('disabled', true);

        // Check the box and update UI
        $('#tax_compliance_mode').prop('checked', true);
        $('.slider-text').text('مفعل');

        // Hide the warning with animation
        $('#tax_compliance_warning').fadeOut(400);

        // Store the activation timestamp and user
        var now = new Date().toISOString();
        $('<input>').attr({
            type: 'hidden',
            name: 'settings[tax_compliance_mode]',
            value: '1'
        }).appendTo('#settings-form');

        $('<input>').attr({
            type: 'hidden',
            name: 'settings[tax_compliance_mode_activated_at]',
            value: now
        }).appendTo('#settings-form');

        $('<input>').attr({
            type: 'hidden',
            name: 'settings[tax_compliance_mode_activated_by]',
            value: '<?= get_staff_user_id(); ?>'
        }).appendTo('#settings-form');

        // Show success message before submit
        setTimeout(function() {
            $btn.html('<i class="fa fa-check" style="margin-left: 8px;"></i>تم التفعيل!');
            setTimeout(function() {
                $('#settings-form').submit();
            }, 500);
        }, 1000);
    });

    // Cancel tax compliance mode with animation - Arabic Version
    $('#cancel_tax_compliance').on('click', function() {
        // Hide the warning with smooth animation
        $('#tax_compliance_warning').fadeOut(400);
        // Make sure the box is unchecked
        $('#tax_compliance_mode').prop('checked', false);
        $('.slider-text').text('معطل');

        // Scroll back to toggle
        $('html, body').animate({
            scrollTop: $('.switch').offset().top - 200
        }, 600, 'easeInOutQuart');
    });

    // Add hover effects for interactive elements
    $('.switch').hover(
        function() {
            $(this).find('.slider').css('transform', 'scale(1.05)');
        },
        function() {
            $(this).find('.slider').css('transform', 'scale(1)');
        }
    );

    // Add smooth transitions
    $('.slider').css('transition', 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)');
});
</script>
