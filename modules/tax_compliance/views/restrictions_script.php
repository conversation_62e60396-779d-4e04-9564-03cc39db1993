<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<?php
// Tax compliance restrictions are active when the module is loaded
// No need for separate setting - module activation = tax compliance active
echo '<!-- Tax Compliance Module: ACTIVE -->';
?>
<script>
$(document).ready(function() {
    console.log('Tax Compliance Mode: Loading restrictions...');

    // Add visual indicator that tax compliance mode is active
    setTimeout(function() {
        if ($('.content-wrapper .content').length > 0) {
            $('.content-wrapper .content').prepend(
                '<div class="alert alert-warning tax-compliance-indicator" style="margin-bottom: 20px; border-left: 5px solid #f39c12; background: #fff3cd; border-color: #ffeaa7;">' +
                '<h4 style="color: #856404; margin-top: 0;"><i class="fa fa-shield"></i> Tax Compliance Mode is Active</h4>' +
                '<p style="color: #856404; margin: 0;">Document editing restrictions are enforced to ensure tax compliance.</p>' +
                '</div>'
            );
        } else if ($('.content-wrapper').length > 0) {
            $('.content-wrapper').prepend(
                '<div class="alert alert-warning tax-compliance-indicator" style="margin: 15px; border-left: 5px solid #f39c12; background: #fff3cd; border-color: #ffeaa7;">' +
                '<h4 style="color: #856404; margin-top: 0;"><i class="fa fa-shield"></i> Tax Compliance Mode is Active</h4>' +
                '<p style="color: #856404; margin: 0;">Document editing restrictions are enforced to ensure tax compliance.</p>' +
                '</div>'
            );
        }
    }, 500);

    var currentUrl = window.location.href;
    console.log('Tax Compliance - Current URL:', currentUrl);

    // Check if we're on an invoice page
    if (currentUrl.indexOf('/admin/invoices/') !== -1) {
        var invoiceId = getInvoiceIdFromUrl();
        console.log('Invoice ID detected:', invoiceId);
        if (invoiceId) {
            checkInvoiceRestrictions(invoiceId);
        }
    }

    // Check if we're on a credit note page
    if (currentUrl.indexOf('/admin/credit_notes/') !== -1) {
        var creditNoteId = getCreditNoteIdFromUrl();
        console.log('Credit Note ID detected:', creditNoteId);
        if (creditNoteId) {
            checkCreditNoteRestrictions(creditNoteId);
        }
    }

    // More aggressive blocking - check every 2 seconds for new elements
    setInterval(function() {
        applyTaxComplianceRestrictions();
    }, 2000);

    // Apply restrictions immediately
    setTimeout(function() {
        applyTaxComplianceRestrictions();
    }, 1000);

    // Enhance module list styling for tax compliance module
    if (window.location.href.indexOf('admin/modules') !== -1) {
        // Function to remove deactivate links for Tax Compliance Module
        function removeTaxComplianceDeactivateLinks() {
            $('table tbody tr').each(function() {
                var $row = $(this);
                var moduleText = $row.find('td:first').text();

                if (moduleText.indexOf('Tax Compliance Mode') !== -1 || moduleText.indexOf('وضع الامتثال الضريبي') !== -1) {
                    // More aggressive removal of deactivate links
                    var $actionCell = $row.find('td:first');

                    // Remove any links that contain "deactivate" but not "secure"
                    $actionCell.find('a').each(function() {
                        var $link = $(this);
                        var linkText = $link.text().toLowerCase().trim();
                        var linkHref = $link.attr('href') || '';

                        // Remove default deactivate links (but keep secure deactivate)
                        if ((linkText.includes('deactivate') || linkText === 'deactivate') &&
                            !linkText.includes('secure') &&
                            !linkText.includes('admin only')) {
                            console.log('Removing deactivate link:', linkText, linkHref);
                            $link.remove();
                        }
                    });

                    // Remove standalone "Deactivate" text nodes
                    $actionCell.contents().each(function() {
                        if (this.nodeType === 3) { // Text node
                            var text = this.textContent.toLowerCase().trim();
                            if (text === 'deactivate' || text.includes('deactivate')) {
                                $(this).remove();
                            }
                        }
                    });

                    // Clean up separators and extra spaces
                    var html = $actionCell.html();
                    if (html) {
                        // Remove multiple separators
                        html = html.replace(/(\s*\|\s*){2,}/g, ' | ');
                        // Remove leading/trailing separators
                        html = html.replace(/^\s*\|\s*/, '').replace(/\s*\|\s*$/, '');
                        // Remove extra whitespace
                        html = html.replace(/\s+/g, ' ').trim();
                        $actionCell.html(html);
                    }

                    // Add prominent modern styling for active tax compliance module
                    $row.addClass('tax-compliance-module-row-modern');
                    $row.css({
                        'background': 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                        'border': '2px solid rgba(102, 126, 234, 0.2)',
                        'border-radius': '12px',
                        'box-shadow': '0 8px 25px rgba(102, 126, 234, 0.15)',
                        'position': 'relative',
                        'overflow': 'hidden'
                    });

                    // Add animated border effect
                    $row.prepend('<div class="module-border-animation"></div>');

                    // Add enhanced security badges
                    var securityBadges = '<div style="margin-top: 12px; display: flex; gap: 8px; flex-wrap: wrap;">' +
                        '<span class="security-badge security-badge-primary">' +
                        '<i class="fa fa-shield-alt" style="margin-right: 6px;"></i>Security Enhanced' +
                        '</span>' +
                        '<span class="security-badge security-badge-success">' +
                        '<i class="fa fa-lock" style="margin-right: 6px;"></i>OTP Protected' +
                        '</span>' +
                        '<span class="security-badge security-badge-info">' +
                        '<i class="fa fa-globe" style="margin-right: 6px;"></i>Multilingual' +
                        '</span>' +
                        '</div>';

                    $row.find('td:first p').after(securityBadges);

                    // Add hover effect
                    $row.on('mouseenter', function() {
                        $(this).css({
                            'transform': 'translateY(-2px)',
                            'box-shadow': '0 12px 35px rgba(102, 126, 234, 0.25)'
                        });
                    }).on('mouseleave', function() {
                        $(this).css({
                            'transform': 'translateY(0)',
                            'box-shadow': '0 8px 25px rgba(102, 126, 234, 0.15)'
                        });
                    });
                }
            });
        }

        // Run the function multiple times to ensure deactivate links are removed
        removeTaxComplianceDeactivateLinks(); // Run immediately
        setTimeout(removeTaxComplianceDeactivateLinks, 100); // Run after 100ms
        setTimeout(removeTaxComplianceDeactivateLinks, 500); // Run after 500ms
        setTimeout(removeTaxComplianceDeactivateLinks, 1000); // Run after 1s

        // Also run when the table is updated (e.g., after DataTables initialization)
        if (typeof $.fn.dataTable !== 'undefined') {
            $(document).on('draw.dt', function() {
                setTimeout(removeTaxComplianceDeactivateLinks, 50);
            });
        }
    }

    function applyTaxComplianceRestrictions() {
        // Block invoice edit/delete buttons for non-draft invoices
        $('a[href*="/admin/invoices/invoice/"], a[href*="/admin/invoices/delete/"]').each(function() {
            var $this = $(this);
            if (!$this.hasClass('tax-compliance-checked')) {
                $this.addClass('tax-compliance-checked');

                // Check if this is for a non-draft invoice
                var href = $this.attr('href') || '';
                var invoiceId = null;
                var matches = href.match(/\/admin\/invoices\/(?:invoice|delete)\/(\d+)/);
                if (matches) {
                    invoiceId = matches[1];
                }

                if (invoiceId && !isDraftInvoice()) {
                    $this.addClass('tax-compliance-disabled');
                    $this.attr('data-original-href', href);
                    $this.attr('href', 'javascript:void(0);');
                    $this.css({
                        'opacity': '0.5',
                        'cursor': 'not-allowed',
                        'pointer-events': 'none'
                    });

                    $this.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        showTaxComplianceAlert('invoice');
                        return false;
                    });
                }
            }
        });

        // Block all credit note edit/delete actions
        $('a[href*="/admin/credit_notes/credit_note/"], a[href*="/admin/credit_notes/delete/"]').each(function() {
            var $this = $(this);
            if (!$this.hasClass('tax-compliance-checked')) {
                $this.addClass('tax-compliance-checked tax-compliance-disabled');
                var href = $this.attr('href') || '';
                $this.attr('data-original-href', href);
                $this.attr('href', 'javascript:void(0);');
                $this.css({
                    'opacity': '0.5',
                    'cursor': 'not-allowed',
                    'pointer-events': 'none'
                });

                $this.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    showTaxComplianceAlert('credit_note');
                    return false;
                });
            }
        });

        // Block delete buttons with onclick handlers
        $('a[onclick*="delete_invoice"], button[onclick*="delete_invoice"]').each(function() {
            var $this = $(this);
            if (!$this.hasClass('tax-compliance-checked')) {
                $this.addClass('tax-compliance-checked');

                if (!isDraftInvoice()) {
                    $this.addClass('tax-compliance-disabled');
                    $this.attr('data-original-onclick', $this.attr('onclick'));
                    $this.attr('onclick', 'return false;');
                    $this.css({
                        'opacity': '0.5',
                        'cursor': 'not-allowed'
                    });

                    $this.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        showTaxComplianceAlert('invoice');
                        return false;
                    });
                }
            }
        });
    }

    function showTaxComplianceAlert(type) {
        var message = '';
        if (type === 'invoice') {
            message = 'This action is not allowed in Tax Compliance Mode. Only draft invoices can be edited or deleted.';
        } else if (type === 'credit_note') {
            message = 'This action is not allowed in Tax Compliance Mode. Credit notes cannot be edited or deleted.';
        }

        // Use the system's alert_float function if available
        if (typeof alert_float === 'function') {
            alert_float('warning', message, 5000);
        } else {
            alert(message);
        }

        console.log('Tax Compliance Alert:', message);
    }
    
    // Function to get invoice ID from URL
    function getInvoiceIdFromUrl() {
        var matches = window.location.href.match(/\/admin\/invoices\/(?:invoice|list_invoices)\/(\d+)/);
        return matches ? matches[1] : null;
    }

    // Function to get credit note ID from URL
    function getCreditNoteIdFromUrl() {
        var matches = window.location.href.match(/\/admin\/credit_notes\/(?:credit_note|list_credit_notes)\/(\d+)/);
        return matches ? matches[1] : null;
    }

    // Function to check if invoice is draft (simple check based on page elements)
    function isDraftInvoice(invoiceId) {
        // Check for draft status indicators on the page
        var statusElement = $('.label-invoice-status, .invoice-status');
        var isDraft = statusElement.hasClass('label-draft') ||
                     statusElement.text().toLowerCase().indexOf('draft') !== -1 ||
                     statusElement.text().toLowerCase().indexOf('مسودة') !== -1; // Arabic for draft

        console.log('Invoice status check - isDraft:', isDraft);
        return isDraft;
    }
    
    // Function to check invoice restrictions
    function checkInvoiceRestrictions(invoiceId) {
        // Get invoice status from the page
        var statusElement = $('.label-invoice-status');
        var isDraft = statusElement.hasClass('label-draft') || statusElement.text().toLowerCase().indexOf('draft') !== -1;
        var isRecurring = $('.recurring-invoice-info').length > 0;
        
        if (!isDraft && !isRecurring) {
            // This is a finalized invoice - apply restrictions
            disableInvoiceEditing();
            showTaxComplianceWarning('invoice');
        }
    }
    
    // Function to check credit note restrictions
    function checkCreditNoteRestrictions(creditNoteId) {
        // Credit notes cannot be edited in tax compliance mode
        disableCreditNoteEditing();
        showTaxComplianceWarning('credit_note');
    }
    
    // Function to disable invoice editing
    function disableInvoiceEditing() {
        // Disable edit buttons
        $('a[href*="/admin/invoices/invoice/"]').each(function() {
            if ($(this).attr('href').indexOf('invoice/' + getInvoiceIdFromUrl()) !== -1) {
                $(this).addClass('disabled').attr('onclick', 'return false;').css({
                    'opacity': '0.5',
                    'cursor': 'not-allowed',
                    'pointer-events': 'none'
                });
            }
        });
        
        // Disable delete buttons
        $('a[onclick*="delete_invoice"]').addClass('disabled').attr('onclick', 'return false;').css({
            'opacity': '0.5',
            'cursor': 'not-allowed',
            'pointer-events': 'none'
        });
        
        // Disable form submission if we're on the edit page
        if (window.location.href.indexOf('/admin/invoices/invoice/') !== -1) {
            $('form#invoice-form').on('submit', function(e) {
                e.preventDefault();
                alert('<?php echo _l('tax_compliance_invoice_edit_blocked'); ?>');
                return false;
            });
            
            // Disable all form inputs
            $('form#invoice-form input, form#invoice-form select, form#invoice-form textarea').prop('disabled', true);
            $('form#invoice-form .btn-primary').addClass('disabled').prop('disabled', true);
        }
    }
    
    // Function to disable credit note editing
    function disableCreditNoteEditing() {
        // Disable edit buttons
        $('a[href*="/admin/credit_notes/credit_note/"]').addClass('disabled').attr('onclick', 'return false;').css({
            'opacity': '0.5',
            'cursor': 'not-allowed',
            'pointer-events': 'none'
        });
        
        // Disable delete buttons
        $('a[onclick*="delete_credit_note"]').addClass('disabled').attr('onclick', 'return false;').css({
            'opacity': '0.5',
            'cursor': 'not-allowed',
            'pointer-events': 'none'
        });
        
        // Disable form submission if we're on the edit page
        if (window.location.href.indexOf('/admin/credit_notes/credit_note/') !== -1) {
            $('form').on('submit', function(e) {
                e.preventDefault();
                alert('<?php echo _l('tax_compliance_credit_note_edit_blocked'); ?>');
                return false;
            });
            
            // Disable all form inputs
            $('form input, form select, form textarea').prop('disabled', true);
            $('form .btn-primary').addClass('disabled').prop('disabled', true);
        }
    }
    
    // Function to show tax compliance warning
    function showTaxComplianceWarning(type) {
        var warningHtml = '';
        
        if (type === 'invoice') {
            warningHtml = '<?php echo get_tax_compliance_warning("invoice"); ?>';
        } else if (type === 'credit_note') {
            warningHtml = '<?php echo get_tax_compliance_warning("credit_note"); ?>';
        }
        
        // Insert warning at the top of the content area
        if (warningHtml && $('.content-wrapper .content').length > 0) {
            $('.content-wrapper .content').prepend(warningHtml);
        }
    }
});
</script>

<style>
/* Tax Compliance Mode Styles */
.tax-compliance-disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.tax-compliance-warning {
    margin-bottom: 20px;
    border-left: 5px solid #f39c12;
    background: #fff3cd;
    border-color: #ffeaa7;
}

.tax-compliance-warning h4 {
    color: #856404;
    margin-top: 0;
}

.tax-compliance-warning p, .tax-compliance-warning li {
    color: #856404;
}

/* Modern Enhanced styling for tax compliance module in modules list */
.tax-compliance-module-row-modern {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.tax-compliance-module-row-modern td {
    border: none !important;
    padding: 20px !important;
}

.tax-compliance-module-row-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.25) !important;
}

/* Animated border effect */
.module-border-animation {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
    animation: borderSlide 3s infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes borderSlide {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

/* Security badges */
.security-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    position: relative;
    overflow: hidden;
    z-index: 2;
}

.security-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.security-badge:hover::before {
    left: 100%;
}

.security-badge-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.security-badge-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.security-badge-info {
    background: linear-gradient(135deg, #17a2b8 0%, #**********%);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

/* Responsive design for module badges */
@media (max-width: 768px) {
    .tax-compliance-module-row-modern [style*="display: flex"] {
        flex-direction: column !important;
        gap: 4px !important;
    }

    .security-badge {
        font-size: 9px;
        padding: 3px 8px;
    }
}
</style>
