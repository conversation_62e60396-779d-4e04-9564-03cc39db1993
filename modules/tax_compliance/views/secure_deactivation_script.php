<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<script>
$(document).ready(function() {
    let otpTimer;
    let currentStep = 1;

    // Define CSRF variables if not already defined
    if (typeof csrf_token_name === 'undefined') {
        window.csrf_token_name = '<?= $this->security->get_csrf_token_name(); ?>';
    }
    if (typeof csrf_hash === 'undefined') {
        window.csrf_hash = '<?= $this->security->get_csrf_hash(); ?>';
    }
    if (typeof admin_url === 'undefined') {
        window.admin_url = '<?= admin_url(); ?>';
    }

    // Initialize secure deactivation functionality
    function initSecureDeactivation() {
        // Load deactivation info
        loadDeactivationInfo();

        // Bind event handlers
        bindEventHandlers();
    }
    
    // Load deactivation information
    function loadDeactivationInfo() {
        var ajaxData = {};
        ajaxData[window.csrf_token_name] = window.csrf_hash;

        $.ajax({
            url: window.admin_url + 'tax_compliance/secure/get_deactivation_info',
            type: 'GET',
            dataType: 'json',
            data: ajaxData,
            success: function(response) {
                if (response.success) {
                    $('#admin-email-display').text(response.admin_email);

                    if (response.otp_pending) {
                        showStep(2);
                        startOtpTimer(response.time_remaining);
                        updateAttemptsDisplay(response.attempts_left);
                    }
                }
            },
            error: function() {
                showAlert('error', 'Error loading deactivation information.');
            }
        });
    }
    
    // Bind event handlers
    function bindEventHandlers() {
        // Request OTP button
        $('#request-otp-btn').on('click', function() {
            requestOtp();
        });
        
        // Verify OTP button
        $('#verify-otp-btn').on('click', function() {
            verifyOtp();
        });
        
        // Resend OTP button
        $('#resend-otp-btn').on('click', function() {
            requestOtp();
        });
        
        // OTP input handling
        $('#otp-input').on('input', function() {
            let value = $(this).val().replace(/\D/g, '');
            $(this).val(value);
            
            if (value.length === 6) {
                $('#verify-otp-btn').prop('disabled', false);
            } else {
                $('#verify-otp-btn').prop('disabled', true);
            }
        });
        
        // Enter key on OTP input
        $('#otp-input').on('keypress', function(e) {
            if (e.which === 13 && $(this).val().length === 6) {
                verifyOtp();
            }
        });
        
        // Close success button
        $('#close-success-btn').on('click', function() {
            $('#taxComplianceDeactivationModal').modal('hide');
            // Redirect to modules page
            window.location.href = admin_url + 'modules';
        });
        
        // Modal close event
        $('#taxComplianceDeactivationModal').on('hidden.bs.modal', function() {
            resetModal();
        });
    }
    
    // Request OTP
    function requestOtp() {
        let $btn = $('#request-otp-btn, #resend-otp-btn');
        let originalText = $btn.html();

        // Show loading state
        $btn.html('<i class="fa fa-spinner fa-spin"></i> Sending...');
        $btn.prop('disabled', true);

        var ajaxData = {};
        ajaxData[window.csrf_token_name] = window.csrf_hash;

        $.ajax({
            url: window.admin_url + 'tax_compliance/secure/request_deactivation_otp',
            type: 'POST',
            dataType: 'json',
            data: ajaxData,
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    showStep(2);
                    startOtpTimer(600); // 10 minutes
                    updateAttemptsDisplay(5);
                    $('#otp-input').focus();
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function() {
                showAlert('error', 'Request failed. Please try again.');
            },
            complete: function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }
        });
    }
    
    // Verify OTP
    function verifyOtp() {
        let otp = $('#otp-input').val();
        
        if (otp.length !== 6) {
            showAlert('error', 'Please enter a valid 6-digit verification code.');
            return;
        }
        
        let $btn = $('#verify-otp-btn');
        let originalText = $btn.html();
        
        // Show loading state
        $btn.html('<i class="fa fa-spinner fa-spin"></i> Verifying...');
        $btn.prop('disabled', true);

        var ajaxData = {
            otp: otp
        };
        ajaxData[window.csrf_token_name] = window.csrf_hash;

        $.ajax({
            url: window.admin_url + 'tax_compliance/secure/verify_and_deactivate',
            type: 'POST',
            dataType: 'json',
            data: ajaxData,
            success: function(response) {
                if (response.success) {
                    clearOtpTimer();
                    showStep(3);
                    showAlert('success', response.message);
                } else {
                    showAlert('error', response.message);
                    
                    // Update attempts if provided
                    if (response.attempts_left !== undefined) {
                        updateAttemptsDisplay(response.attempts_left);
                    }
                    
                    // Clear OTP input on failed attempt
                    $('#otp-input').val('').focus();
                }
            },
            error: function() {
                showAlert('error', 'Verification failed. Please try again.');
            },
            complete: function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }
        });
    }
    
    // Show specific step
    function showStep(step) {
        // Hide all steps and remove active class
        $('.step-container').hide().removeClass('step-active');

        // Show current step with animation
        switch(step) {
            case 1:
                $('#step-request-otp').show().addClass('step-active');
                break;
            case 2:
                $('#step-verify-otp').show().addClass('step-active');
                setTimeout(() => $('#otp-input').focus(), 300);
                break;
            case 3:
                $('#step-success').show().addClass('step-active');
                break;
        }

        currentStep = step;
    }
    
    // Start OTP timer
    function startOtpTimer(seconds) {
        clearOtpTimer();
        
        function updateTimer() {
            if (seconds <= 0) {
                $('.timer-display').html('<span style="color: #dc3545;">Code Expired</span>');
                clearOtpTimer();
                return;
            }

            let minutes = Math.floor(seconds / 60);
            let remainingSeconds = seconds % 60;

            $('.timer-display').html(
                '<i class="fa fa-clock-o"></i> ' +
                minutes + ':' + (remainingSeconds < 10 ? '0' : '') + remainingSeconds
            );

            seconds--;
        }
        
        updateTimer();
        otpTimer = setInterval(updateTimer, 1000);
    }
    
    // Clear OTP timer
    function clearOtpTimer() {
        if (otpTimer) {
            clearInterval(otpTimer);
            otpTimer = null;
        }
    }
    
    // Update attempts display
    function updateAttemptsDisplay(attemptsLeft) {
        if (attemptsLeft > 0) {
            $('.attempts-display').html(
                'Attempts: ' + attemptsLeft + ' left'
            );
        } else {
            $('.attempts-display').html(
                '<span style="color: #dc3545;">No attempts left</span>'
            );
        }
    }
    
    // Reset modal to initial state
    function resetModal() {
        showStep(1);
        clearOtpTimer();
        $('#otp-input').val('');
        $('#verify-otp-btn').prop('disabled', true);
        $('.alert').remove();
    }
    
    // Show alert message
    function showAlert(type, message) {
        // Remove existing alerts
        $('.modal-body .alert').remove();
        
        let alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        let iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
        
        let alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible" style="margin-bottom: 20px; border-radius: 8px;">' +
            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<i class="fa ' + iconClass + '" style="margin-right: 8px;"></i>' +
            message +
            '</div>';
        
        $('.modal-body').prepend(alertHtml);
        
        // Auto-hide success alerts after 3 seconds
        if (type === 'success') {
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);
        }
    }
    
    // Initialize when modal is shown
    $('#taxComplianceDeactivationModal').on('shown.bs.modal', function() {
        initSecureDeactivation();
    });
    
    // Global function to open the modal
    window.openTaxComplianceDeactivationModal = function() {
        $('#taxComplianceDeactivationModal').modal('show');
    };
});
</script>
