<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Tax Compliance Secure Deactivation Controller
 * 
 * Handles secure deactivation of tax compliance module with OTP verification
 * 
 * <AUTHOR> MaCno
 * @link https://github.com/chareen3
 */
class Tax_compliance_secure extends AdminController
{
    public function __construct()
    {
        parent::__construct();

        // Only admins can access this controller
        if (!is_admin()) {
            access_denied('Tax Compliance Secure Operations');
        }

        // Load required helpers and models
        $this->load->helper('tax_compliance/tax_compliance');
        $this->load->library('form_validation');

        // Additional security checks
        $this->_security_checks();
    }

    /**
     * Additional security checks
     */
    private function _security_checks()
    {
        // Check if request is from valid admin session
        if (!$this->session->userdata('staff_logged_in')) {
            show_error('Invalid session', 403);
        }

        // Check for suspicious activity patterns
        $this->_check_rate_limiting();

        // Log all access attempts
        log_activity('Tax Compliance Secure Controller accessed by staff ID: ' . get_staff_user_id() . ' from IP: ' . $this->input->ip_address());
    }

    /**
     * Check rate limiting for security
     */
    private function _check_rate_limiting()
    {
        $ip = $this->input->ip_address();
        $staff_id = get_staff_user_id();
        $current_time = time();

        // Check IP-based rate limiting (max 10 requests per hour)
        $ip_key = 'tax_compliance_ip_' . md5($ip);
        $ip_requests = $this->session->userdata($ip_key) ?: [];

        // Clean old requests (older than 1 hour)
        $ip_requests = array_filter($ip_requests, function($timestamp) use ($current_time) {
            return ($current_time - $timestamp) < 3600;
        });

        if (count($ip_requests) >= 10) {
            log_activity('Tax Compliance: Rate limit exceeded for IP: ' . $ip);
            show_error('Rate limit exceeded. Please try again later.', 429);
        }

        // Add current request
        $ip_requests[] = $current_time;
        $this->session->set_userdata($ip_key, $ip_requests);

        // Check staff-based rate limiting (max 5 OTP requests per hour)
        $staff_key = 'tax_compliance_staff_' . $staff_id;
        $staff_requests = $this->session->userdata($staff_key) ?: [];

        // Clean old requests
        $staff_requests = array_filter($staff_requests, function($timestamp) use ($current_time) {
            return ($current_time - $timestamp) < 3600;
        });

        $this->session->set_userdata($staff_key, $staff_requests);
    }

    /**
     * Request OTP for module deactivation
     */
    public function request_deactivation_otp()
    {
        // Verify CSRF token
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        // Check if user can deactivate the module
        if (!can_deactivate_tax_compliance_module()) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => _l('tax_compliance_deactivation_access_denied')
                ]));
            return;
        }

        // Enhanced rate limiting: Check if OTP was requested recently
        $staff_id = get_staff_user_id();
        $current_time = time();

        // Check last request time (minimum 60 seconds between requests)
        $last_request = $this->session->userdata('tax_compliance_last_otp_request');
        if ($last_request && ($current_time - $last_request) < 60) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => _l('tax_compliance_otp_rate_limit')
                ]));
            return;
        }

        // Check hourly limit for this staff member
        $staff_key = 'tax_compliance_staff_' . $staff_id;
        $staff_requests = $this->session->userdata($staff_key) ?: [];

        if (count($staff_requests) >= 5) {
            log_activity('Tax Compliance: Hourly OTP limit exceeded for staff ID: ' . $staff_id);
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Hourly limit exceeded. Please try again later.'
                ]));
            return;
        }

        // Generate OTP
        $otp = generate_tax_compliance_otp();
        
        // Store OTP in session
        store_tax_compliance_otp($otp);
        
        // Get current admin details
        $admin = $this->staff_model->get(get_staff_user_id());
        
        // Send OTP email
        $email_sent = send_tax_compliance_otp_email(
            $otp,
            $admin->email,
            $admin->firstname . ' ' . $admin->lastname
        );

        if ($email_sent) {
            // Update last request time
            $this->session->set_userdata('tax_compliance_last_otp_request', $current_time);

            // Update staff request tracking
            $staff_requests[] = $current_time;
            $this->session->set_userdata($staff_key, $staff_requests);

            // Log the request with additional security info
            log_activity('Tax Compliance deactivation OTP requested by: ' . $admin->firstname . ' ' . $admin->lastname .
                        ' (IP: ' . $this->input->ip_address() . ', User Agent: ' . $this->input->user_agent() . ')');

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => true,
                    'message' => _l('tax_compliance_otp_sent_success'),
                    'email' => $this->mask_email($admin->email)
                ]));
        } else {
            // Log failed attempt
            log_activity('Tax Compliance: Failed to send OTP to ' . $admin->email . ' for staff ID: ' . $staff_id);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => _l('tax_compliance_otp_send_failed')
                ]));
        }
    }

    /**
     * Verify OTP and deactivate module
     */
    public function verify_and_deactivate()
    {
        // Verify CSRF token
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        // Check if user can deactivate the module
        if (!can_deactivate_tax_compliance_module()) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => _l('tax_compliance_deactivation_access_denied')
                ]));
            return;
        }

        // Validate input with additional security checks
        $this->form_validation->set_rules('otp', _l('tax_compliance_otp_code'), 'required|exact_length[6]|numeric');

        if (!$this->form_validation->run()) {
            // Log invalid format attempt
            log_activity('Tax Compliance: Invalid OTP format submitted by staff ID: ' . get_staff_user_id() . ' from IP: ' . $this->input->ip_address());

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => _l('tax_compliance_otp_invalid_format')
                ]));
            return;
        }

        // Additional security: Check if there's an active OTP session
        if (!$this->session->userdata('tax_compliance_otp')) {
            log_activity('Tax Compliance: OTP verification attempted without active session by staff ID: ' . get_staff_user_id());

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'No active verification session. Please request a new code.'
                ]));
            return;
        }

        $provided_otp = $this->input->post('otp');
        
        // Verify OTP
        $verification_result = verify_tax_compliance_otp($provided_otp);
        
        if (!$verification_result['success']) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => $verification_result['message']
                ]));
            return;
        }

        // OTP verified successfully, proceed with deactivation
        try {
            // Deactivate the module
            $deactivation_result = $this->app_modules->deactivate('tax_compliance');
            
            if ($deactivation_result) {
                // Comprehensive logging for audit trail
                $admin = $this->staff_model->get(get_staff_user_id());
                $log_message = 'Tax Compliance Module DEACTIVATED by: ' . $admin->firstname . ' ' . $admin->lastname .
                              ' (Staff ID: ' . get_staff_user_id() . ', Email: ' . $admin->email .
                              ', IP: ' . $this->input->ip_address() . ', User Agent: ' . $this->input->user_agent() .
                              ', Timestamp: ' . date('Y-m-d H:i:s') . ', OTP Verified: YES)';

                log_activity($log_message);

                // Clear all security-related session data
                $this->session->unset_userdata([
                    'tax_compliance_last_otp_request',
                    'tax_compliance_staff_' . get_staff_user_id(),
                    'tax_compliance_ip_' . md5($this->input->ip_address())
                ]);

                // Send notification email to all admins about deactivation
                $this->_notify_admins_of_deactivation($admin);

                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'success' => true,
                        'message' => _l('tax_compliance_deactivated_success'),
                        'redirect' => admin_url('modules')
                    ]));
            } else {
                log_activity('Tax Compliance: Module deactivation FAILED for staff ID: ' . get_staff_user_id());

                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'success' => false,
                        'message' => _l('tax_compliance_deactivation_failed')
                    ]));
            }
        } catch (Exception $e) {
            log_activity('Tax Compliance Module deactivation failed: ' . $e->getMessage());
            
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => _l('tax_compliance_deactivation_error')
                ]));
        }
    }

    /**
     * Get deactivation status and information
     */
    public function get_deactivation_info()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $admin = $this->staff_model->get(get_staff_user_id());
        $otp_pending = $this->session->userdata('tax_compliance_otp') ? true : false;
        $otp_expires = $this->session->userdata('tax_compliance_otp_expires');
        $attempts_left = 5 - ($this->session->userdata('tax_compliance_otp_attempts') ?: 0);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'can_deactivate' => can_deactivate_tax_compliance_module(),
                'admin_email' => $this->mask_email($admin->email),
                'otp_pending' => $otp_pending,
                'otp_expires' => $otp_expires,
                'attempts_left' => max(0, $attempts_left),
                'time_remaining' => $otp_expires ? max(0, $otp_expires - time()) : 0
            ]));
    }

    /**
     * Notify all admins about tax compliance deactivation
     *
     * @param object $deactivating_admin
     */
    private function _notify_admins_of_deactivation($deactivating_admin)
    {
        try {
            // Get all admin users
            $this->db->where('admin', 1);
            $this->db->where('active', 1);
            $admins = $this->db->get(db_prefix() . 'staff')->result();

            foreach ($admins as $admin) {
                if ($admin->staffid != $deactivating_admin->staffid) {
                    // Send notification email to other admins
                    $this->load->library('email');
                    $this->email->clear(true);
                    $this->email->from(get_option('smtp_email'), get_option('companyname'));
                    $this->email->to($admin->email);
                    $this->email->subject('SECURITY ALERT: Tax Compliance Mode Deactivated');

                    $message = "SECURITY ALERT: Tax Compliance Mode has been deactivated.\n\n";
                    $message .= "Deactivated by: " . $deactivating_admin->firstname . " " . $deactivating_admin->lastname . "\n";
                    $message .= "Email: " . $deactivating_admin->email . "\n";
                    $message .= "Time: " . date('Y-m-d H:i:s') . "\n";
                    $message .= "IP Address: " . $this->input->ip_address() . "\n\n";
                    $message .= "All financial document restrictions have been removed.\n";
                    $message .= "Please review this action if it was not authorized.\n\n";
                    $message .= "This is an automated security notification.";

                    $this->email->message($message);
                    $this->email->send();
                }
            }
        } catch (Exception $e) {
            log_activity('Tax Compliance: Failed to send admin notifications: ' . $e->getMessage());
        }
    }

    /**
     * Mask email address for security
     *
     * @param string $email
     * @return string
     */
    private function mask_email($email)
    {
        $parts = explode('@', $email);
        if (count($parts) != 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 2) {
            return $email;
        }

        $masked_username = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
        return $masked_username . '@' . $domain;
    }
}
