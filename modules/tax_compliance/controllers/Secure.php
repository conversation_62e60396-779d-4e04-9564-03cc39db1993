<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Tax Compliance Secure Operations Controller
 * 
 * Simplified controller for secure deactivation operations
 * 
 * <AUTHOR>
 * @link https://github.com/chareen3
 */
class Secure extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        
        // Only admins can access this controller
        if (!is_admin()) {
            show_error('Access denied. Admin privileges required.', 403);
        }
        
        // Load required helpers
        $this->load->helper('tax_compliance/tax_compliance');
        $this->load->library('form_validation');
    }

    /**
     * Get deactivation information
     */
    public function get_deactivation_info()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $staff_id = get_staff_user_id();
        $admin = $this->staff_model->get($staff_id);

        if (!$admin) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'User not found. Please login again.'
                ]));
            return;
        }

        $otp_status = get_tax_compliance_otp_status($staff_id);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'can_deactivate' => can_deactivate_tax_compliance_module(),
                'admin_email' => $this->mask_email($admin->email),
                'admin_name' => $admin->firstname . ' ' . $admin->lastname,
                'otp_pending' => $otp_status['has_active_otp'],
                'attempts_left' => $otp_status['attempts_left'],
                'time_remaining' => $otp_status['time_remaining']
            ]));
    }

    /**
     * Request OTP for deactivation
     */
    public function request_deactivation_otp()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        // Check rate limiting - prevent multiple requests within 1 minute
        $staff_id = get_staff_user_id();
        $this->db->where('staff_id', $staff_id);
        $this->db->where('created_at >', date('Y-m-d H:i:s', time() - 60));
        $recent_requests = $this->db->count_all_results(db_prefix() . 'tax_compliance_otp');

        if ($recent_requests > 0) {
            log_tax_compliance_audit($staff_id, 'otp_rate_limited', 'OTP request blocked due to rate limiting');
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Please wait before requesting another verification code.'
                ]));
            return;
        }

        // Generate and store OTP
        $otp = generate_tax_compliance_otp();
        store_tax_compliance_otp($otp);
        
        // Get admin details
        $admin = $this->staff_model->get(get_staff_user_id());
        
        // Send OTP email
        $email_sent = send_tax_compliance_otp_email(
            $otp,
            $admin->email,
            $admin->firstname . ' ' . $admin->lastname
        );

        if ($email_sent) {
            log_tax_compliance_audit($staff_id, 'otp_requested', 'OTP verification code sent to: ' . $admin->email);
            log_activity('Tax Compliance deactivation OTP requested by: ' . $admin->firstname . ' ' . $admin->lastname);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => true,
                    'message' => 'Verification code sent successfully to your email address.',
                    'email' => $this->mask_email($admin->email)
                ]));
        } else {
            log_tax_compliance_audit($staff_id, 'otp_send_failed', 'Failed to send OTP to: ' . $admin->email);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Failed to send verification code. Please try again.'
                ]));
        }
    }

    /**
     * Verify OTP and deactivate module
     */
    public function verify_and_deactivate()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $this->form_validation->set_rules('otp', 'OTP Code', 'required|exact_length[6]|numeric');
        
        if (!$this->form_validation->run()) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Please enter a valid 6-digit verification code.'
                ]));
            return;
        }

        $provided_otp = $this->input->post('otp');
        
        // Verify OTP
        $verification_result = verify_tax_compliance_otp($provided_otp);
        
        if (!$verification_result['success']) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => $verification_result['message']
                ]));
            return;
        }

        // Deactivate the module
        try {
            $deactivation_result = $this->app_modules->deactivate('tax_compliance');
            
            if ($deactivation_result) {
                $staff_id = get_staff_user_id();
                $admin = $this->staff_model->get($staff_id);

                // Log comprehensive audit trail
                log_tax_compliance_audit($staff_id, 'module_deactivated', 'Tax Compliance Module deactivated successfully', [
                    'admin_name' => $admin->firstname . ' ' . $admin->lastname,
                    'admin_email' => $admin->email,
                    'verification_method' => 'email_otp'
                ]);

                log_activity('Tax Compliance Module DEACTIVATED by: ' . $admin->firstname . ' ' . $admin->lastname . ' (OTP verified)');

                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'success' => true,
                        'message' => 'Tax Compliance Mode has been successfully deactivated.',
                        'redirect' => admin_url('modules')
                    ]));
            } else {
                log_tax_compliance_audit(get_staff_user_id(), 'deactivation_failed', 'Module deactivation failed');

                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'success' => false,
                        'message' => 'Failed to deactivate Tax Compliance Mode. Please try again.'
                    ]));
            }
        } catch (Exception $e) {
            log_activity('Tax Compliance Module deactivation failed: ' . $e->getMessage());
            
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'An error occurred during deactivation. Please contact support.'
                ]));
        }
    }

    /**
     * Mask email address for security
     */
    private function mask_email($email)
    {
        $parts = explode('@', $email);
        if (count($parts) != 2) {
            return $email;
        }
        
        $username = $parts[0];
        $domain = $parts[1];
        
        if (strlen($username) <= 2) {
            return $email;
        }
        
        $masked_username = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
        return $masked_username . '@' . $domain;
    }
}
