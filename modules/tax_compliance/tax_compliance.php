<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Tax Compliance Mode
Description: Provides tax compliance functionality with restrictions on invoice and credit note editing
Version: 1.0.0
Requires at least: 2.3.*
Author: <PERSON><PERSON> MaCno
Author URI: https://github.com/chareen3
*/

define('TAX_COMPLIANCE_MODULE_NAME', 'tax_compliance');

// Load module helper functions
if (!function_exists('is_tax_compliance_mode_enabled')) {
    require_once(__DIR__ . '/helpers/tax_compliance_helper.php');
}

// Register hooks for tax compliance functionality
hooks()->add_action('admin_init', 'tax_compliance_module_init_menu_items');
hooks()->add_action('admin_init', 'tax_compliance_permissions');
// Use multiple hooks to ensure we catch the restrictions
hooks()->add_action('app_admin_head', 'tax_compliance_check_restrictions');
hooks()->add_action('before_render_content', 'tax_compliance_check_restrictions');

// No need for settings section - module activation handles everything

// Hook into invoice and credit note processing
hooks()->add_filter('before_update_invoice', 'tax_compliance_before_update_invoice');
hooks()->add_filter('before_create_credit_note', 'tax_compliance_before_create_credit_note');
hooks()->add_filter('before_update_credit_note', 'tax_compliance_before_update_credit_note');

// Hook into invoice status changes
hooks()->add_action('invoice_status_changed', 'tax_compliance_invoice_status_changed');

// Hook into settings processing
hooks()->add_filter('before_settings_updated', 'tax_compliance_before_settings_updated');

// Hook to add frontend restrictions script
hooks()->add_action('app_admin_footer', 'tax_compliance_add_restrictions_script');

// Hook to add dashboard notification
hooks()->add_action('before_start_render_dashboard_content', 'tax_compliance_add_dashboard_notification');

// Hook to add number field restrictions
hooks()->add_action('after_settings_group_view', 'tax_compliance_lock_number_fields');

// Hook to customize module action links
hooks()->add_filter('module_tax_compliance_action_links', 'tax_compliance_custom_action_links');

// Additional hook to remove default deactivate links before they're added
hooks()->add_action('admin_init', 'tax_compliance_remove_default_deactivate_links');

// Hook to ensure secure controller is available
hooks()->add_action('admin_init', 'tax_compliance_ensure_secure_controller');

/**
 * Ensure secure controller is available for module operations
 */
function tax_compliance_ensure_secure_controller()
{
    // This function ensures the secure controller is properly loaded
    // The actual routing is handled by Perfex CRM's module system
}

/**
 * Initialize tax compliance module permissions
 */
function tax_compliance_permissions()
{
    $capabilities = [];

    $capabilities['capabilities'] = [
        'view'   => _l('permission_view') . '(' . _l('permission_global') . ')',
        'manage' => _l('permission_edit'),
    ];

    register_staff_capabilities('tax_compliance', $capabilities, _l('tax_compliance_mode'));
}

/**
 * Initialize tax compliance module menu items
 */
function tax_compliance_module_init_menu_items()
{
    $CI = &get_instance();

    // Add menu item to settings if user has permission
    if (staff_can('manage', 'tax_compliance') || is_admin()) {
        // This will be handled through the existing settings interface
        // No additional menu items needed as it integrates with existing settings
    }
}

/**
 * Check tax compliance restrictions on admin pages
 */
function tax_compliance_check_restrictions()
{
    // Tax compliance restrictions are active when module is loaded
    error_log('Tax Compliance: Module active, checking restrictions...');

    $CI = &get_instance();

    // Get current URL segments
    $segments = $CI->uri->segment_array();

    // Check if we're in admin area
    if (!isset($segments[1]) || $segments[1] !== 'admin') {
        return;
    }

    // Get controller and method
    $controller = isset($segments[2]) ? $segments[2] : '';
    $method = isset($segments[3]) ? $segments[3] : '';
    $id = isset($segments[4]) ? $segments[4] : '';

    error_log('Tax Compliance: Controller: ' . $controller . ', Method: ' . $method . ', ID: ' . $id);

    // Check if we're in the invoices controller
    if ($controller === 'invoices') {
        // Block invoice editing/deletion based on method and invoice status
        if (in_array($method, ['invoice', 'delete', 'update_number_settings']) && $id && is_numeric($id)) {
            error_log('Tax Compliance: Checking invoice restrictions for ID: ' . $id);

            $CI->load->model('invoices_model');
            $invoice = $CI->invoices_model->get($id);

            if ($invoice) {
                error_log('Tax Compliance: Invoice found, status: ' . $invoice->status);

                // Check if action is allowed
                $action = ($method === 'delete') ? 'delete_invoice' : 'edit_invoice';

                if (!is_action_allowed_in_tax_compliance_mode($action, $invoice)) {
                    error_log('Tax Compliance: Blocking action: ' . $action . ' for invoice ' . $id);

                    // Block the action with proper alert
                    if ($method === 'delete') {
                        set_alert('warning', 'Deleting this invoice is not allowed in tax compliance mode. Only draft invoices can be deleted.');
                    } else {
                        set_alert('warning', 'Editing this invoice is not allowed in tax compliance mode. Only draft invoices and recurring invoices can be edited.');
                    }

                    redirect(admin_url('invoices/list_invoices/' . $id));
                    exit;
                } else {
                    error_log('Tax Compliance: Action allowed: ' . $action . ' for invoice ' . $id);
                }
            } else {
                error_log('Tax Compliance: Invoice not found for ID: ' . $id);
            }
        }
    }

    // Check if we're in the credit_notes controller
    if ($controller === 'credit_notes') {
        if (in_array($method, ['credit_note', 'delete']) && $id && is_numeric($id)) {
            error_log('Tax Compliance: Checking credit note restrictions for ID: ' . $id);

            $action = ($method === 'delete') ? 'delete_credit_note' : 'edit_credit_note';

            if (!is_action_allowed_in_tax_compliance_mode($action)) {
                error_log('Tax Compliance: Blocking action: ' . $action . ' for credit note ' . $id);

                // Block the action with proper alert
                if ($method === 'delete') {
                    set_alert('warning', 'Deleting credit notes is not allowed in tax compliance mode.');
                } else {
                    set_alert('warning', 'Editing credit notes is not allowed in tax compliance mode.');
                }

                redirect(admin_url('credit_notes/list_credit_notes/' . $id));
                exit;
            } else {
                error_log('Tax Compliance: Action allowed: ' . $action . ' for credit note ' . $id);
            }
        }
    }
}

/**
 * Add tax compliance settings to general settings page
 */
function tax_compliance_add_to_general_settings($group)
{
    $CI = &get_instance();

    // Only show on the general settings page
    if (isset($group['id']) && $group['id'] === 'general') {
        // Only show to admins or users with tax compliance permissions
        if (staff_can('manage', 'tax_compliance') || is_admin()) {
            // Get the current staff member's language preference
            $current_language = get_staff_default_language();

            // If no staff language is set, fall back to system default
            if (empty($current_language)) {
                $current_language = get_option('active_language');
            }

            // Load Arabic version if language is Arabic, otherwise load English
            if ($current_language === 'arabic') {
                $CI->load->view('tax_compliance/settings_arabic');
            } else {
                $CI->load->view('tax_compliance/settings');
            }
        }
    }
}

/**
 * Lock number fields in settings when tax compliance module is active
 */
function tax_compliance_lock_number_fields($group)
{
    // Always lock fields when the module is active (no need for separate setting)
    // The module activation itself means tax compliance is required

    $CI = &get_instance();

    // Check which settings group we're on
    $group_id = isset($group['id']) ? $group['id'] : '';

    // Fields to lock based on the settings group
    $fields_to_lock = [];

    switch ($group_id) {
        case 'invoices':
            $fields_to_lock = [
                'next_invoice_number',
                'invoice_number_format',
                'invoice_prefix'
            ];
            break;

        case 'estimates':
            $fields_to_lock = [
                'next_estimate_number',
                'estimate_number_format',
                'estimate_prefix'
            ];
            break;

        case 'credit_notes':
            $fields_to_lock = [
                'next_credit_note_number',
                'credit_note_number_format',
                'credit_note_prefix'
            ];
            break;
    }

    if (!empty($fields_to_lock)) {
        // Add JavaScript to lock the fields
        echo '<script>
        $(document).ready(function() {
            console.log("Tax Compliance: Locking number fields...");

            // Lock number fields for tax compliance
            var fieldsToLock = ' . json_encode($fields_to_lock) . ';
            console.log("Fields to lock:", fieldsToLock);

            // Function to apply locks
            function applyFieldLocks() {
                fieldsToLock.forEach(function(fieldName) {
                    console.log("Checking field:", fieldName);

                    // Try multiple selectors to find the field
                    var $field = $(\'input[name="settings[\' + fieldName + \']"]\');

                    // Also check for radio buttons (for number format fields)
                    if ($field.length === 0) {
                        $field = $(\'input[name="settings[\' + fieldName + \']"]:radio\');
                    }

                    console.log("Found field elements:", $field.length);

                    if ($field.length > 0) {
                        console.log("Locking field:", fieldName);

                        // Disable the field(s)
                        $field.each(function() {
                            var $this = $(this);
                            $this.prop(\'disabled\', true);
                            $this.css({
                                \'background-color\': \'#f5f5f5\',
                                \'cursor\': \'not-allowed\',
                                \'opacity\': \'0.7\'
                            });

                            // For radio buttons, also disable the labels
                            if ($this.is(":radio")) {
                                $this.closest(\'.radio\').css({
                                    \'opacity\': \'0.5\',
                                    \'cursor\': \'not-allowed\'
                                });
                                $this.closest(\'.radio\').find(\'label\').css({
                                    \'cursor\': \'not-allowed\'
                                });
                            }
                        });

                        // Add a lock icon and explanation
                        var $wrapper = $field.first().closest(\'.form-group\');
                        if ($wrapper.length === 0) {
                            $wrapper = $field.first().parent();
                        }

                        if ($wrapper.length > 0 && !$wrapper.find(\'.tax-compliance-lock\').length) {
                            $wrapper.append(
                                \'<div class="tax-compliance-lock" style="margin-top: 8px; padding: 8px 12px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border: 1px solid #ffeaa7; border-radius: 4px; color: #856404; font-size: 12px;">\' +
                                \'<i class="fa fa-lock" style="color: #f39c12; margin-right: 5px;"></i>\' +
                                \'<strong>Locked by Tax Compliance Mode</strong> - This field cannot be modified to maintain audit trail integrity.\' +
                                \'</div>\'
                            );
                        }
                    } else {
                        console.log("Field not found:", fieldName);
                    }
                });
            }

            // Apply locks immediately
            applyFieldLocks();

            // Apply locks again after a delay (in case content loads dynamically)
            setTimeout(applyFieldLocks, 1000);

            // Show warning message at the top of the settings page
            if (fieldsToLock.length > 0) {
                var warningHtml = \'<div class="alert alert-warning tax-compliance-numbers-warning" style="margin-bottom: 20px; border-left: 5px solid #f39c12; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-color: #ffeaa7;">\' +
                    \'<h4 style="margin-top: 0; color: #856404;"><i class="fa fa-shield"></i> Tax Compliance Mode Active</h4>\' +
                    \'<p style="margin: 0; color: #856404;">Document numbering fields are locked to prevent tampering and maintain audit trail integrity. \' +
                    \'Only administrators can modify these settings when tax compliance mode is disabled.</p>\' +
                    \'</div>\';

                // Try different selectors to find where to insert the warning
                if ($(\'.panel-body\').length > 0) {
                    $(\'.panel-body\').first().prepend(warningHtml);
                } else if ($(\'.content\').length > 0) {
                    $(\'.content\').first().prepend(warningHtml);
                } else if ($(\'form\').length > 0) {
                    $(\'form\').first().prepend(warningHtml);
                }
            }
        });
        </script>';

        // Add CSS for better styling
        echo '<style>
        .tax-compliance-lock {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 8px;
        }

        .tax-compliance-lock i {
            color: #f39c12;
            margin-right: 5px;
        }

        .tax-compliance-numbers-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffeaa7;
        }

        .tax-compliance-numbers-warning h4 {
            color: #856404;
        }

        .tax-compliance-numbers-warning p {
            color: #856404;
        }
        </style>';
    }
}

/**
 * Handle before invoice update to enforce tax compliance restrictions
 */
function tax_compliance_before_update_invoice($hookData, $invoice_id)
{
    // Debug: Log that this function is being called
    error_log('Tax Compliance: before_update_invoice hook called with ID: ' . $invoice_id);

    if (!is_tax_compliance_mode_enabled()) {
        error_log('Tax Compliance: Mode not enabled, allowing update');
        return $hookData;
    }

    error_log('Tax Compliance: Mode enabled, checking restrictions for invoice ' . $invoice_id);

    $CI = &get_instance();

    if ($invoice_id) {
        $CI->load->model('invoices_model');
        $invoice = $CI->invoices_model->get($invoice_id);

        if ($invoice && !is_action_allowed_in_tax_compliance_mode('edit_invoice', $invoice)) {
            error_log('Tax Compliance: Blocking invoice update for invoice ' . $invoice_id . ' (Status: ' . $invoice->status . ')');

            // Set error message and prevent update
            $CI->session->set_flashdata('alert-type', 'warning');
            $CI->session->set_flashdata('message', 'Editing this invoice is not allowed in tax compliance mode. Only draft invoices and recurring invoices can be edited.');

            // Stop execution and redirect
            redirect(admin_url('invoices/list_invoices/' . $invoice_id));
            exit; // Make sure we stop here
        } else {
            error_log('Tax Compliance: Invoice update allowed for invoice ' . $invoice_id . ' (Status: ' . ($invoice ? $invoice->status : 'not found') . ')');
        }
    }

    return $hookData;
}

/**
 * Handle before credit note creation to enforce tax compliance restrictions
 */
function tax_compliance_before_create_credit_note($hookData)
{
    error_log('Tax Compliance: before_create_credit_note hook called');

    if (!is_tax_compliance_mode_enabled()) {
        error_log('Tax Compliance: Mode not enabled, allowing credit note creation');
        return $hookData;
    }

    error_log('Tax Compliance: Mode enabled, applying credit note creation restrictions');

    // In tax compliance mode, credit notes can be created but with restrictions
    // Remove any credits_used to prevent automatic application
    if (isset($hookData['data']['credits_used'])) {
        $hookData['data']['credits_used'] = 0;
    }

    return $hookData;
}

/**
 * Handle before credit note update to enforce tax compliance restrictions
 */
function tax_compliance_before_update_credit_note($hookData, $credit_note_id)
{
    error_log('Tax Compliance: before_update_credit_note hook called with ID: ' . $credit_note_id);

    if (!is_tax_compliance_mode_enabled()) {
        error_log('Tax Compliance: Mode not enabled, allowing credit note update');
        return $hookData;
    }

    error_log('Tax Compliance: Mode enabled, checking credit note update restrictions');

    $CI = &get_instance();

    // Credit notes cannot be edited in tax compliance mode
    if (!is_action_allowed_in_tax_compliance_mode('edit_credit_note')) {
        error_log('Tax Compliance: Blocking credit note update for credit note ' . $credit_note_id);

        $CI->session->set_flashdata('alert-type', 'warning');
        $CI->session->set_flashdata('message', 'Editing credit notes is not allowed in tax compliance mode.');

        redirect(admin_url('credit_notes/list_credit_notes/' . $credit_note_id));
        exit; // Make sure we stop here
    }

    return $hookData;
}

/**
 * Handle invoice status changes for tax compliance logging
 */
function tax_compliance_invoice_status_changed($data)
{
    if (!is_tax_compliance_mode_enabled()) {
        return;
    }

    // Log status changes for audit trail in tax compliance mode
    $invoice_id = $data['invoice_id'];
    $status = $data['status'];

    log_activity('Tax Compliance: Invoice status changed [Invoice ID: ' . $invoice_id . ', New Status: ' . $status . ']');
}

/**
 * Handle settings update to manage tax compliance mode activation and protect number fields
 */
function tax_compliance_before_settings_updated($data)
{
    // Handle tax compliance mode activation
    if (isset($data['settings']['tax_compliance_mode']) && $data['settings']['tax_compliance_mode'] == '1') {
        // If tax compliance mode is being enabled, record activation details
        if (get_option('tax_compliance_mode') != '1') {
            $data['settings']['tax_compliance_mode_activated_at'] = date('Y-m-d H:i:s');
            $data['settings']['tax_compliance_mode_activated_by'] = get_staff_user_id();

            // Log the activation
            log_activity('Tax Compliance Mode activated by staff ID: ' . get_staff_user_id());
        }
    }

    // Protect number fields when tax compliance module is active
        // List of protected fields
        $protected_fields = [
            'next_invoice_number',
            'invoice_number_format',
            'invoice_prefix',
            'next_estimate_number',
            'estimate_number_format',
            'estimate_prefix',
            'next_credit_note_number',
            'credit_note_number_format',
            'credit_note_prefix'
        ];

        // Remove protected fields from the update data
        foreach ($protected_fields as $field) {
            if (isset($data['settings'][$field])) {
                // Log the attempt
                error_log('Tax Compliance: Blocked attempt to modify protected field: ' . $field);
                log_activity('Tax Compliance: Blocked attempt to modify protected field: ' . $field . ' by staff ID: ' . get_staff_user_id());

                // Remove the field from the update
                unset($data['settings'][$field]);

                // Set an alert to inform the user
                $CI = &get_instance();
                set_alert('warning', 'The field "' . $field . '" cannot be modified while Tax Compliance Mode is active.');
            }
        }

    return $data;
}

/**
 * Register activation hook
 */
register_activation_hook(TAX_COMPLIANCE_MODULE_NAME, 'tax_compliance_module_activation_hook');

function tax_compliance_module_activation_hook()
{
    $CI = &get_instance();
    require_once(__DIR__ . '/install.php');
}

/**
 * Register deactivation hook
 */
register_deactivation_hook(TAX_COMPLIANCE_MODULE_NAME, 'tax_compliance_module_deactivation_hook');

function tax_compliance_module_deactivation_hook()
{
    // Clean up any temporary data or cache if needed
    // The module functions will no longer be available after deactivation
}

/**
 * Add dashboard notification for tax compliance module
 */
function tax_compliance_add_dashboard_notification()
{
    // Always show notification when module is active

    echo '<div class="col-lg-12 tw-mt-1.5">';
    echo '<div class="alert alert-warning alert-dismissible tax-compliance-dashboard-alert" role="alert" style="border-left: 5px solid #f39c12;">';
    echo '<button type="button" class="close" data-dismiss="alert" aria-label="' . _l('close') . '">';
    echo '<span aria-hidden="true">&times;</span>';
    echo '</button>';
    echo '<h4 class="alert-title tw-mb-0 tw-flex tw-items-center tw-space-x-2">';
    echo '<i class="fa fa-shield"></i>';
    echo '<span>' . _l('tax_compliance_dashboard_active_title') . '</span>';
    echo '</h4>';
    echo '<hr />';
    echo '<p class="tw-mb-0">' . _l('tax_compliance_dashboard_active_message') . '</p>';
    echo '</div>';
    echo '</div>';
}

/**
 * Remove default deactivate links for tax compliance module
 */
function tax_compliance_remove_default_deactivate_links()
{
    // Add a filter to intercept and modify the modules list before rendering
    hooks()->add_filter('modules_list_data', function($modules) {
        foreach ($modules as &$module) {
            if ($module['system_name'] === 'tax_compliance') {
                // Force the module to use custom action links only
                $module['custom_action_links_only'] = true;
            }
        }
        return $modules;
    });
}

/**
 * Customize tax compliance module action links
 */
function tax_compliance_custom_action_links($actions)
{
    // Remove ALL deactivate-related links (more aggressive filtering)
    $actions = array_filter($actions, function($action) {
        // Convert to lowercase for case-insensitive matching
        $action_lower = strtolower($action);

        // Remove any link that contains deactivate, disable, or similar terms
        return strpos($action_lower, 'deactivate') === false &&
               strpos($action_lower, 'disable') === false &&
               strpos($action, 'modules/deactivate/') === false &&
               strpos($action, '_l(\'module_deactivate\')') === false &&
               !preg_match('/>\s*deactivate\s*</i', $action_lower);
    });

    // Only show secure deactivation for admins
    if (is_admin()) {
        // Add secure deactivation link
        $actions[] = '<a href="javascript:void(0);" onclick="if(typeof openTaxComplianceDeactivationModal === \'function\') { openTaxComplianceDeactivationModal(); } else { alert(\'Secure deactivation feature loading...\'); }" class="text-warning" style="font-weight: 600;">
            <i class="fa fa-shield"></i> Secure Deactivate
        </a>';
    } else {
        // For non-admins, show that only admins can deactivate
        $actions[] = '<span class="text-muted" style="font-style: italic;">
            <i class="fa fa-lock"></i> Admin Only
        </span>';
    }

    return $actions;
}

/**
 * Add frontend restrictions script to admin pages
 */
function tax_compliance_add_restrictions_script()
{
    $CI = &get_instance();

    // Load on all admin pages to show the indicator
    $CI->load->view('tax_compliance/restrictions_script');

    // Load secure deactivation modal and script on modules page
    $current_uri = $CI->uri->uri_string();
    if (strpos($current_uri, 'admin/modules') !== false) {
        $CI->load->view('tax_compliance/modals/secure_deactivation');
        $CI->load->view('tax_compliance/secure_deactivation_script');
    }
}

/**
 * Register language files
 */
register_language_files(TAX_COMPLIANCE_MODULE_NAME, [TAX_COMPLIANCE_MODULE_NAME]);
