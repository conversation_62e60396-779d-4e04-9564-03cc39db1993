<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Add the tax compliance mode setting if it doesn't exist
if (!get_option('tax_compliance_mode')) {
    add_option('tax_compliance_mode', '0');
}

// Add setting for when it was activated (timestamp)
if (!get_option('tax_compliance_mode_activated_at')) {
    add_option('tax_compliance_mode_activated_at', '');
}

// Add setting for who activated it (staff ID)
if (!get_option('tax_compliance_mode_activated_by')) {
    add_option('tax_compliance_mode_activated_by', '');
}

// Create OTP verification table for secure deactivation
if (!$CI->db->table_exists(db_prefix() . 'tax_compliance_otp')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'tax_compliance_otp` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `staff_id` int(11) NOT NULL,
        `otp_code` varchar(6) NOT NULL,
        `email` varchar(255) NOT NULL,
        `ip_address` varchar(45) NOT NULL,
        `user_agent` text,
        `attempts` int(11) DEFAULT 0,
        `max_attempts` int(11) DEFAULT 5,
        `expires_at` datetime NOT NULL,
        `verified` tinyint(1) DEFAULT 0,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `staff_id` (`staff_id`),
        KEY `otp_code` (`otp_code`),
        KEY `expires_at` (`expires_at`),
        KEY `verified` (`verified`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8;');
}

// Create audit log table for tax compliance actions
if (!$CI->db->table_exists(db_prefix() . 'tax_compliance_audit')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'tax_compliance_audit` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `staff_id` int(11) NOT NULL,
        `action` varchar(100) NOT NULL,
        `description` text,
        `ip_address` varchar(45) NOT NULL,
        `user_agent` text,
        `additional_data` text,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `staff_id` (`staff_id`),
        KEY `action` (`action`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8;');
}

// Add OTP settings
if (!get_option('tax_compliance_otp_expiry_minutes')) {
    add_option('tax_compliance_otp_expiry_minutes', '10');
}

if (!get_option('tax_compliance_max_otp_attempts')) {
    add_option('tax_compliance_max_otp_attempts', '5');
}

if (!get_option('tax_compliance_rate_limit_minutes')) {
    add_option('tax_compliance_rate_limit_minutes', '1');
}

if (!get_option('tax_compliance_max_hourly_requests')) {
    add_option('tax_compliance_max_hourly_requests', '5');
}

// Log the installation
if (file_exists(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php')) {
    $CI->load->helper('file');
    $content = "\n" . 'Tax Compliance Mode module with Enhanced Security installed at: ' . date('Y-m-d H:i:s');
    write_file(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php', $content, 'a+');
}
