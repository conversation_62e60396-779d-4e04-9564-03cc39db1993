<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Tax Compliance Mode Helper Functions
 *
 * <AUTHOR>
 * @link https://github.com/chareen3
 */

/**
 * Check if tax compliance mode is enabled
 * When the module is active, tax compliance is always enabled
 *
 * @return boolean
 */
function is_tax_compliance_mode_enabled()
{
    // Tax compliance is active when the module is loaded
    // No need for separate database setting
    error_log('Tax Compliance: Module active - ENABLED');
    return true;
}

/**
 * Check if the current action is allowed in tax compliance mode
 * 
 * @param string $action The action to check (edit_invoice, delete_invoice, merge_invoice, edit_credit_note, delete_credit_note)
 * @param mixed $item The item to check (optional)
 * @return boolean
 */
function is_action_allowed_in_tax_compliance_mode($action, $item = null)
{
    if (!is_tax_compliance_mode_enabled()) {
        return true;
    }

    // All actions are restricted in tax compliance mode except for specific exceptions
    switch ($action) {
        case 'edit_invoice':
            // Allow editing draft invoices (not finalized yet)
            if (isset($item) && is_object($item) && isset($item->status)) {
                // Draft invoices can be edited
                if ($item->status == 6) { // STATUS_DRAFT
                    return true;
                }
                // Recurring invoices can be edited
                if (isset($item->recurring) && $item->recurring > 0) {
                    return true;
                }
            }
            return false;

        case 'delete_invoice':
            // Only allow deleting draft invoices
            if (isset($item) && is_object($item) && isset($item->status)) {
                // Only draft invoices can be deleted
                if ($item->status == 6) { // STATUS_DRAFT
                    return true;
                }
            }
            return false;

        case 'edit_refund':
        case 'delete_refund':
            // Allow refund operations as they are part of compliance
            return true;

        case 'merge_invoice':
        case 'edit_credit_note':
        case 'delete_credit_note':
        case 'edit_invoice_number':
        case 'edit_credit_note_number':
            // These actions are always restricted in tax compliance mode
            return false;

        default:
            // If we don't explicitly handle an action, restrict it to be safe
            return false;
    }
}

/**
 * Check if a specific invoice field can be edited in tax compliance mode
 *
 * @param string $field_name The field name to check
 * @param object $invoice Invoice object for context
 * @return boolean
 */
function is_invoice_field_editable_in_tax_compliance_mode($field_name, $invoice = null)
{
    // If tax compliance mode is not enabled, all fields are editable
    if (!is_tax_compliance_mode_enabled()) {
        return true;
    }

    // If no invoice provided, restrict all fields
    if (!$invoice) {
        return false;
    }

    // Draft invoices are fully editable
    if (isset($invoice->status) && $invoice->status == 6) { // STATUS_DRAFT
        return true;
    }

    // For finalized invoices, only allow editing specific fields
    $allowed_fields = ['allowed_payment_modes', 'sale_agent', 'adminnote', 'tags'];
    
    return in_array($field_name, $allowed_fields);
}

/**
 * Check if invoice line items can be modified in tax compliance mode
 *
 * @param object $invoice Invoice object for context
 * @return boolean
 */
function can_modify_invoice_items_in_tax_compliance_mode($invoice = null)
{
    // If tax compliance mode is not enabled, items can be modified
    if (!is_tax_compliance_mode_enabled()) {
        return true;
    }

    // If no invoice provided, restrict modifications
    if (!$invoice) {
        return false;
    }

    // Only draft invoices can have items modified
    return isset($invoice->status) && $invoice->status == 6; // STATUS_DRAFT
}

/**
 * Generate a 6-digit OTP code for tax compliance module deactivation
 *
 * @return string 6-digit OTP code
 */
function generate_tax_compliance_otp()
{
    return str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Store OTP in database with expiration time
 *
 * @param string $otp The OTP code
 * @param int $expiry_minutes Expiry time in minutes (default: 10)
 * @return bool Success status
 */
function store_tax_compliance_otp($otp, $expiry_minutes = 10)
{
    $CI = &get_instance();

    $staff_id = get_staff_user_id();
    $admin = $CI->staff_model->get($staff_id);

    // Clean up any existing OTP for this user
    $CI->db->where('staff_id', $staff_id);
    $CI->db->where('verified', 0);
    $CI->db->delete(db_prefix() . 'tax_compliance_otp');

    // Insert new OTP
    $data = [
        'staff_id' => $staff_id,
        'otp_code' => $otp,
        'email' => $admin->email,
        'ip_address' => $CI->input->ip_address(),
        'user_agent' => $CI->input->user_agent(),
        'attempts' => 0,
        'max_attempts' => get_option('tax_compliance_max_otp_attempts', 5),
        'expires_at' => date('Y-m-d H:i:s', time() + ($expiry_minutes * 60)),
        'verified' => 0
    ];

    return $CI->db->insert(db_prefix() . 'tax_compliance_otp', $data);
}

/**
 * Verify OTP code for tax compliance module deactivation
 *
 * @param string $provided_otp The OTP provided by user
 * @return array Result with success status and message
 */
function verify_tax_compliance_otp($provided_otp)
{
    $CI = &get_instance();

    $staff_id = get_staff_user_id();

    // Get the latest OTP for this user
    $CI->db->where('staff_id', $staff_id);
    $CI->db->where('verified', 0);
    $CI->db->where('expires_at >', date('Y-m-d H:i:s'));
    $CI->db->order_by('created_at', 'DESC');
    $CI->db->limit(1);
    $otp_record = $CI->db->get(db_prefix() . 'tax_compliance_otp')->row();

    // Check if OTP exists
    if (!$otp_record) {
        return ['success' => false, 'message' => 'No valid OTP found. Please request a new one.'];
    }

    // Check if OTP has expired
    if (strtotime($otp_record->expires_at) <= time()) {
        return ['success' => false, 'message' => 'OTP has expired. Please request a new one.'];
    }

    // Check attempt limit
    if ($otp_record->attempts >= $otp_record->max_attempts) {
        return ['success' => false, 'message' => 'Too many failed attempts. Please request a new OTP.'];
    }

    // Increment attempts
    $CI->db->where('id', $otp_record->id);
    $CI->db->update(db_prefix() . 'tax_compliance_otp', [
        'attempts' => $otp_record->attempts + 1,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    // Verify OTP
    if ($provided_otp === $otp_record->otp_code) {
        // Mark as verified
        $CI->db->where('id', $otp_record->id);
        $CI->db->update(db_prefix() . 'tax_compliance_otp', [
            'verified' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // Log successful verification
        log_tax_compliance_audit($staff_id, 'otp_verified', 'OTP verification successful for deactivation');

        return ['success' => true, 'message' => 'OTP verified successfully.'];
    }

    // Log failed attempt
    log_tax_compliance_audit($staff_id, 'otp_failed', 'Invalid OTP attempt: ' . $provided_otp);

    $remaining_attempts = $otp_record->max_attempts - ($otp_record->attempts + 1);
    return ['success' => false, 'message' => 'Invalid OTP. ' . $remaining_attempts . ' attempts remaining.'];
}

/**
 * Send OTP email for tax compliance module deactivation
 *
 * @param string $otp The OTP code
 * @param string $admin_email Admin email address
 * @param string $admin_name Admin name
 * @return bool Success status
 */
function send_tax_compliance_otp_email($otp, $admin_email, $admin_name)
{
    $CI = &get_instance();
    $CI->load->library('email');

    // Email configuration
    $CI->email->clear(true);
    $CI->email->from(get_option('smtp_email'), get_option('companyname'));
    $CI->email->to($admin_email);
    $CI->email->subject('Tax Compliance Module Deactivation - OTP Verification');

    // Load email template
    $email_template = $CI->load->view('tax_compliance/emails/otp_verification', [
        'otp' => $otp,
        'admin_name' => $admin_name,
        'company_name' => get_option('companyname'),
        'expires_in' => '10 minutes'
    ], true);

    $CI->email->message($email_template);

    if ($CI->email->send()) {
        log_activity('Tax Compliance OTP sent to admin: ' . $admin_email);
        return true;
    } else {
        log_activity('Failed to send Tax Compliance OTP to admin: ' . $admin_email);
        return false;
    }
}

/**
 * Log tax compliance audit trail
 *
 * @param int $staff_id Staff ID
 * @param string $action Action performed
 * @param string $description Description of action
 * @param array $additional_data Additional data to log
 * @return bool Success status
 */
function log_tax_compliance_audit($staff_id, $action, $description, $additional_data = [])
{
    $CI = &get_instance();

    $data = [
        'staff_id' => $staff_id,
        'action' => $action,
        'description' => $description,
        'ip_address' => $CI->input->ip_address(),
        'user_agent' => $CI->input->user_agent(),
        'additional_data' => !empty($additional_data) ? json_encode($additional_data) : null
    ];

    return $CI->db->insert(db_prefix() . 'tax_compliance_audit', $data);
}

/**
 * Check if current user can deactivate tax compliance module
 *
 * @return bool
 */
function can_deactivate_tax_compliance_module()
{
    // Only admins can deactivate the module
    return is_admin();
}

/**
 * Get current OTP status for user
 *
 * @param int $staff_id Staff ID
 * @return array OTP status information
 */
function get_tax_compliance_otp_status($staff_id = null)
{
    $CI = &get_instance();

    if (!$staff_id) {
        $staff_id = get_staff_user_id();
    }

    // Get the latest active OTP
    $CI->db->where('staff_id', $staff_id);
    $CI->db->where('verified', 0);
    $CI->db->where('expires_at >', date('Y-m-d H:i:s'));
    $CI->db->order_by('created_at', 'DESC');
    $CI->db->limit(1);
    $otp_record = $CI->db->get(db_prefix() . 'tax_compliance_otp')->row();

    if (!$otp_record) {
        return [
            'has_active_otp' => false,
            'expires_at' => null,
            'attempts_left' => 0,
            'time_remaining' => 0
        ];
    }

    $expires_timestamp = strtotime($otp_record->expires_at);
    $time_remaining = max(0, $expires_timestamp - time());
    $attempts_left = max(0, $otp_record->max_attempts - $otp_record->attempts);

    return [
        'has_active_otp' => true,
        'expires_at' => $otp_record->expires_at,
        'attempts_left' => $attempts_left,
        'time_remaining' => $time_remaining
    ];
}

/**
 * Get tax compliance warning message for different contexts
 *
 * @param string $context The context (invoice, credit_note, enabling)
 * @return string HTML warning message
 */
function get_tax_compliance_warning($context = 'enabling')
{

    switch ($context) {
        case 'invoice':
            return '<div class="alert alert-warning" style="font-size: 14px; border-left: 5px solid #f39c12;">
                <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> <strong>Tax Compliance Mode Active</strong></h4>
                <p>This invoice is subject to tax compliance regulations. The following restrictions apply:</p>
                <ul>
                    <li>Draft invoices remain fully editable and deletable</li>
                    <li>Finalized invoices have restricted editing (only payment methods, sales agent, admin notes, and tags)</li>
                    <li>Recurring invoices can be managed (cancel/generate recurrence)</li>
                    <li>Changing invoice numbers or prefixes is not allowed</li>
                    <li>Merging invoices is disabled</li>
                    <li>Only draft invoices can be deleted</li>
                </ul>
                <p><strong>Note:</strong> These restrictions ensure compliance with tax regulations and cannot be bypassed.</p>
            </div>';

        case 'credit_note':
            return '<div class="alert alert-warning" style="font-size: 14px; border-left: 5px solid #f39c12;">
                <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> <strong>Tax Compliance Mode Active</strong></h4>
                <p>This credit note is subject to tax compliance regulations. Editing and deletion are restricted to maintain audit trail integrity.</p>
                <p><strong>Note:</strong> These restrictions ensure compliance with tax regulations and cannot be bypassed.</p>
            </div>';

        case 'enabling':
        default:
            return '<div class="alert alert-warning" style="font-size: 14px; border-left: 5px solid #f39c12;">
                <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> <strong>Warning: Secure deactivation requires email verification!</strong></h4>
                <p>Tax Compliance Mode can now be securely deactivated by administrators using email OTP verification.</p>
                <p>The following restrictions will be applied when tax compliance mode is enabled:</p>
                <ul>
                    <li>Draft invoices remain fully editable and deletable</li>
                    <li>Finalized invoices have restricted editing (only payment methods, sales agent, admin notes, and tags)</li>
                    <li>Recurring invoices can be managed (cancel/generate recurrence)</li>
                    <li>Changing invoice numbers or prefixes is not allowed</li>
                    <li>Merging invoices is disabled</li>
                    <li>Only draft invoices can be deleted</li>
                    <li>Credit notes cannot be edited or deleted</li>
                </ul>
                <p><strong>Important:</strong> Only administrators can deactivate this mode using secure email verification.</p>
            </div>';
    }
}
