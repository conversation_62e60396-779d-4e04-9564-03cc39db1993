<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Remove the tax compliance mode settings
delete_option('tax_compliance_mode');
delete_option('tax_compliance_mode_activated_at');
delete_option('tax_compliance_mode_activated_by');

// Log the uninstallation
if (file_exists(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php')) {
    $CI = &get_instance();
    $CI->load->helper('file');
    $content = "\n" . 'Tax Compliance Mode module uninstalled at: ' . date('Y-m-d H:i:s');
    write_file(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php', $content, 'a+');
}
