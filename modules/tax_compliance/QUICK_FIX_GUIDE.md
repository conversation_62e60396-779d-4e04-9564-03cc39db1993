# Tax Compliance Module - Quick Fix Guide

## 🚨 Current Issues and Solutions

### Issue 1: 404 Error on Controller Routes
**Problem:** `tax_compliance_secure/get_deactivation_info` returns 404

**Solution:** The controller routing has been simplified. Use these URLs instead:
```
OLD: admin/tax_compliance_secure/get_deactivation_info
NEW: admin/tax_compliance/secure/get_deactivation_info
```

**Files Updated:**
- ✅ `controllers/Secure.php` (new simplified controller)
- ✅ `views/secure_deactivation_script.php` (updated URLs)

### Issue 2: JavaScript Syntax Errors
**Problem:** PHP language functions in JavaScript causing syntax errors

**Solution:** Replaced all PHP `_l()` calls with static English strings
```javascript
// OLD (causing errors):
'<?= _l('tax_compliance_sending'); ?>'

// NEW (working):
'Sending...'
```

**Files Updated:**
- ✅ `views/secure_deactivation_script.php` (all PHP calls removed)

### Issue 3: CSRF Token Handling
**Problem:** CSRF token not being passed correctly

**Solution:** Ensure CSRF variables are available in JavaScript
```javascript
// Add this to your admin layout if not present:
var csrf_token_name = '<?= $this->security->get_csrf_token_name(); ?>';
var csrf_hash = '<?= $this->security->get_csrf_hash(); ?>';
```

## 🔧 Quick Testing Steps

### Step 1: Test File Structure
```bash
# Navigate to your modules directory and verify:
ls -la modules/tax_compliance/controllers/Secure.php
ls -la modules/tax_compliance/views/modals/secure_deactivation.php
ls -la modules/tax_compliance/views/secure_deactivation_script.php
```

### Step 2: Test Modal Loading
1. Go to **Admin → Modules**
2. Look for the Tax Compliance module with orange highlighting
3. Click **"Secure Deactivate"** link
4. Modal should open (even if OTP doesn't work yet)

### Step 3: Test Controller Access
Visit this URL directly (replace with your domain):
```
https://yourdomain.com/admin/tax_compliance/secure/get_deactivation_info
```

Expected: JSON response (not 404)

### Step 4: Check Browser Console
1. Open browser developer tools (F12)
2. Go to Console tab
3. Look for JavaScript errors
4. Should see no syntax errors

## 🛠️ Manual Fixes Needed

### Fix 1: Ensure Module Controller Loading
Add this to your main CRM's `application/config/routes.php` if the controller still doesn't load:
```php
$route['admin/tax_compliance/secure/(:any)'] = 'modules/tax_compliance/controllers/secure/$1';
```

### Fix 2: Add CSRF Variables to Admin Layout
In your admin layout file (usually `application/views/admin/includes/head.php`), add:
```javascript
<script>
var csrf_token_name = '<?= $this->security->get_csrf_token_name(); ?>';
var csrf_hash = '<?= $this->security->get_csrf_hash(); ?>';
var admin_url = '<?= admin_url(); ?>';
</script>
```

### Fix 3: Email Configuration
Ensure email is configured in **Admin → Settings → Email**:
- SMTP Host
- SMTP Port  
- SMTP Username/Password
- Test email sending

## 🧪 Testing the Implementation

### Test 1: Basic Modal Test
```html
<!-- Add this to any admin page to test modal -->
<button onclick="$('#taxComplianceDeactivationModal').modal('show')">Test Modal</button>
```

### Test 2: OTP Generation Test
```php
// Add this to test OTP functions work
require_once('modules/tax_compliance/helpers/tax_compliance_helper.php');
$otp = generate_tax_compliance_otp();
echo "Generated OTP: " . $otp;
```

### Test 3: Controller Access Test
Visit: `yourdomain.com/modules/tax_compliance/test_secure_deactivation.php`

## 🔍 Debugging Steps

### Check 1: Module Activation
```sql
-- Verify module is active in database
SELECT * FROM tblmodules WHERE module_name = 'tax_compliance';
```

### Check 2: File Permissions
```bash
# Ensure files are readable
chmod 644 modules/tax_compliance/controllers/Secure.php
chmod 644 modules/tax_compliance/views/modals/secure_deactivation.php
```

### Check 3: Error Logs
Check these log files:
- `application/logs/log-[date].php`
- Server error logs
- Browser console errors

### Check 4: Admin Permissions
```php
// Verify current user is admin
var_dump(is_admin()); // Should return true
```

## 🚀 Alternative Simple Implementation

If the above doesn't work, here's a simplified approach:

### Option 1: Direct PHP Processing
Create `modules/tax_compliance/deactivate_handler.php`:
```php
<?php
require_once('../../application/config/config.php');
// Handle OTP and deactivation directly
```

### Option 2: Use Existing Module Structure
Modify the existing settings page to include the secure deactivation form directly instead of using a separate controller.

## 📞 Next Steps

1. **Test the current implementation** using the steps above
2. **Check browser console** for any remaining JavaScript errors  
3. **Verify controller access** by visiting the URLs directly
4. **Test email configuration** by sending a test email from admin panel
5. **Report specific errors** if any issues remain

## ✅ Success Indicators

When working correctly, you should see:
- ✅ No 404 errors on controller routes
- ✅ No JavaScript syntax errors in console
- ✅ Modal opens with proper styling
- ✅ OTP emails are sent successfully
- ✅ Module can be deactivated with valid OTP

---

**Note:** The implementation has been significantly simplified to work with standard Perfex CRM module structure. The core functionality remains the same but uses more straightforward routing and error handling.
