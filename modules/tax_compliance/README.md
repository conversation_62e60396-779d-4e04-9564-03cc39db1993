# Tax Compliance Mode - Enhanced Security Module

## Overview

The Tax Compliance Mode module provides advanced security features for financial document management with secure deactivation capabilities. This module enforces strict controls on invoices, estimates, and credit notes to ensure compliance with tax regulations while maintaining audit trail integrity.

## 🔒 Enhanced Security Features

### Secure Deactivation with OTP Verification
- **6-digit email OTP verification** for module deactivation
- **Admin-only access** with role-based permissions
- **Rate limiting** to prevent abuse (max 5 OTP requests per hour)
- **Session management** with automatic cleanup
- **Comprehensive audit logging** for all actions

### Modern UI/UX Design
- **Professional popup modal** for OTP verification
- **Responsive design** that works on all devices
- **Clean, modern styling** with gradient backgrounds
- **Real-time countdown timer** for OTP expiration
- **Highlighted module styling** in the modules list

### Multi-language Support
- **English and Arabic** language support
- **RTL (Right-to-Left)** layout support for Arabic
- **Comprehensive translations** for all UI elements

## 🚀 Key Features

### Tax Compliance Restrictions
- ✅ Draft invoices remain fully editable and deletable
- 🔒 Finalized invoices have restricted editing (only payment methods, sales agent, admin notes, and tags)
- 🔄 Recurring invoices can be managed (cancel/generate recurrence)
- 🚫 Invoice/estimate/credit note number changes blocked
- 🚫 Invoice merging disabled
- 🚫 Credit note editing and deletion blocked

### Security Enhancements
- 🛡️ **Email OTP verification** for deactivation
- 👤 **Admin role verification** before any action
- ⏱️ **Rate limiting** (60 seconds between requests, 5 per hour)
- 📝 **Comprehensive audit logging** with IP and user agent tracking
- 📧 **Admin notifications** when module is deactivated
- 🔐 **Session security** with automatic cleanup

### User Experience
- 🎨 **Modern, professional UI** with gradient styling
- 📱 **Responsive design** for mobile and desktop
- ⚡ **Real-time feedback** and status updates
- 🌐 **Multi-language support** (English/Arabic)
- 🔔 **Clear error messages** and user guidance

## 📁 File Structure

```
modules/tax_compliance/
├── tax_compliance.php              # Main module file
├── controllers/
│   └── Tax_compliance_secure.php   # Secure deactivation controller
├── helpers/
│   └── tax_compliance_helper.php   # Helper functions
├── views/
│   ├── modals/
│   │   └── secure_deactivation.php # OTP verification modal
│   ├── emails/
│   │   └── otp_verification.php    # OTP email template
│   ├── secure_deactivation_script.php # JavaScript functionality
│   ├── restrictions_script.php     # Tax compliance restrictions
│   ├── settings.php               # English settings view
│   └── settings_arabic.php        # Arabic settings view
├── language/
│   ├── english/
│   │   └── tax_compliance_lang.php # English translations
│   └── arabic/
│       └── tax_compliance_lang.php # Arabic translations
├── install.php                    # Installation script
├── test_functionality.php         # Testing script
└── README.md                      # This file
```

## 🔧 Installation

1. **Upload the module** to your `modules/` directory
2. **Navigate** to Admin → Modules
3. **Activate** the Tax Compliance module
4. The module will automatically apply restrictions

## 💻 Usage

### Activating Tax Compliance Mode
1. Go to **Admin → Settings → General**
2. Scroll to the **Tax Compliance Mode** section
3. Toggle the switch to **ON**
4. Confirm the activation in the warning dialog

### Secure Deactivation Process
1. Go to **Admin → Modules**
2. Find the **Tax Compliance Mode** module (highlighted in orange)
3. Click **"Secure Deactivate"** (only visible to admins)
4. **Request OTP** - A 6-digit code will be sent to your email
5. **Enter the OTP** in the verification modal
6. **Confirm deactivation** - The module will be safely deactivated

### Security Features in Action
- **Rate Limiting**: Maximum 5 OTP requests per hour per admin
- **Session Security**: 60-second cooldown between requests
- **Audit Trail**: All actions logged with IP, timestamp, and user details
- **Admin Notifications**: Other admins are notified of deactivation

## 🛡️ Security Best Practices

### For Administrators
- ✅ Only request OTP when you intend to deactivate
- ✅ Verify the email address before requesting OTP
- ✅ Never share OTP codes with anyone
- ✅ Review audit logs regularly
- ✅ Ensure proper admin role assignments

### For System Security
- 🔒 OTP codes expire in 10 minutes
- 🔒 Maximum 5 verification attempts per OTP
- 🔒 Comprehensive logging of all actions
- 🔒 IP-based and user-based rate limiting
- 🔒 Automatic session cleanup on success/failure

## 🌐 Language Support

### Supported Languages
- **English** (default)
- **Arabic** with RTL support

### Adding New Languages
1. Create a new language directory: `language/[language_code]/`
2. Copy `tax_compliance_lang.php` from English directory
3. Translate all language keys
4. Register the language in the module

## 🧪 Testing

Run the built-in test script to verify functionality:

```php
// Access via browser:
/modules/tax_compliance/test_functionality.php

// Or include in your testing suite:
require_once('modules/tax_compliance/test_functionality.php');
run_all_tests();
```

## 📋 Requirements

- **Perfex CRM** v2.3.0 or higher
- **PHP** 7.4 or higher
- **Admin privileges** for module management
- **Email configuration** for OTP delivery
- **Modern browser** for optimal UI experience

## 🔍 Troubleshooting

### Common Issues

**OTP not received:**
- Check email configuration in Admin → Settings → Email
- Verify spam/junk folders
- Ensure admin email address is correct

**Cannot access secure deactivation:**
- Verify you have admin privileges
- Check if you're logged in properly
- Clear browser cache and try again

**Rate limit exceeded:**
- Wait for the cooldown period (60 seconds between requests)
- Check if you've exceeded hourly limit (5 requests)

### Debug Mode
Enable debug logging by adding to your config:
```php
$config['log_threshold'] = 4; // Enable all logging
```

## 👨‍💻 Author

**Charith MaCno**
- GitHub: [@chareen3](https://github.com/chareen3)
- Module Version: 2.0.0 (Enhanced Security)

## 📄 License

This module follows the same license as Perfex CRM.

## 🔄 Changelog

### Version 2.0.0 (Enhanced Security)
- ✨ Added secure deactivation with OTP verification
- 🛡️ Implemented comprehensive security features
- 🎨 Modern UI/UX with professional styling
- 🌐 Enhanced multi-language support
- 📝 Comprehensive audit logging
- 📧 Admin notification system

### Version 1.0.0 (Original)
- 🔒 Basic tax compliance restrictions
- 📊 Invoice and credit note protection
- 🔢 Number field locking
- 📋 Audit trail maintenance

---

**⚠️ Important Security Notice**
This module handles sensitive financial data and system security. Always test in a development environment before deploying to production. Ensure proper backup procedures are in place before making any changes.
