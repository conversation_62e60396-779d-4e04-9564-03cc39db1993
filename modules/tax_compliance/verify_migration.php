<?php
// Simple verification script for tax compliance database migration
require_once('../../application/config/config.php');
require_once('../../application/config/app-config.php');

// Database connection
$mysqli = new mysqli(APP_DB_HOSTNAME, APP_DB_USERNAME, APP_DB_PASSWORD, APP_DB_NAME);

if ($mysqli->connect_error) {
    die('Connection failed: ' . $mysqli->connect_error);
}

echo "<h2>Tax Compliance Database Migration Verification</h2>";

// Check if tables exist
$tables = ['tbltax_compliance_otp', 'tbltax_compliance_audit'];
$all_good = true;

foreach ($tables as $table) {
    $result = $mysqli->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table $table exists</p>";
        
        // Check record count
        $count_result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
        $count = $count_result->fetch_assoc()['count'];
        echo "<p style='margin-left: 20px;'>Records: $count</p>";
    } else {
        echo "<p style='color: red;'>❌ Table $table missing</p>";
        $all_good = false;
    }
}

// Check settings
$settings = [
    'tax_compliance_otp_expiry_minutes',
    'tax_compliance_max_otp_attempts', 
    'tax_compliance_rate_limit_minutes',
    'tax_compliance_max_hourly_requests'
];

echo "<h3>Settings Check:</h3>";
foreach ($settings as $setting) {
    $result = $mysqli->query("SELECT value FROM tbloptions WHERE name = '$setting'");
    if ($result->num_rows > 0) {
        $value = $result->fetch_assoc()['value'];
        echo "<p style='color: green;'>✅ $setting = $value</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ $setting not found</p>";
    }
}

if ($all_good) {
    echo "<h3 style='color: green;'>✅ Migration Complete!</h3>";
    echo "<p>All database tables and settings are properly configured.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test the secure deactivation in Admin → Modules</li>";
    echo "<li>Verify email configuration for OTP delivery</li>";
    echo "<li>Check browser console for any JavaScript errors</li>";
    echo "</ul>";
} else {
    echo "<h3 style='color: red;'>❌ Migration Issues Found</h3>";
    echo "<p>Some tables or settings are missing. Please run the install script again.</p>";
}

$mysqli->close();
?>
