<!DOCTYPE html>
<html>
<head>
    <title>Tax Compliance - Secure Deactivation Test</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
</head>
<body>
    <div class="container" style="margin-top: 50px;">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-shield"></i> Tax Compliance - Secure Deactivation Test
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="alert alert-info">
                            <strong>Test Environment:</strong> This page tests the secure deactivation functionality.
                        </div>
                        
                        <h4>Test Results:</h4>
                        <div id="test-results">
                            <p><i class="fa fa-spinner fa-spin"></i> Running tests...</p>
                        </div>
                        
                        <hr>
                        
                        <h4>Manual Test:</h4>
                        <button type="button" class="btn btn-warning" onclick="testSecureDeactivation()">
                            <i class="fa fa-shield"></i> Test Secure Deactivation Modal
                        </button>
                        
                        <div style="margin-top: 20px;">
                            <h5>Expected Behavior:</h5>
                            <ul>
                                <li>Modal should open with modern UI</li>
                                <li>Email field should be populated</li>
                                <li>OTP request should work (if email configured)</li>
                                <li>All animations and styling should work</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the secure deactivation modal and scripts -->
    <?php 
    if (file_exists(__DIR__ . '/views/modals/secure_deactivation.php')) {
        include __DIR__ . '/views/modals/secure_deactivation.php';
    }
    if (file_exists(__DIR__ . '/views/secure_deactivation_script.php')) {
        include __DIR__ . '/views/secure_deactivation_script.php';
    }
    ?>

    <script>
    // Mock variables for testing
    var admin_url = '/admin/';
    var csrf_token_name = 'csrf_token';
    var csrf_hash = 'test_hash';
    
    $(document).ready(function() {
        runTests();
    });
    
    function runTests() {
        var results = [];
        
        // Test 1: Check if modal HTML exists
        if ($('#taxComplianceDeactivationModal').length > 0) {
            results.push('<span class="text-success"><i class="fa fa-check"></i> Modal HTML: PASS</span>');
        } else {
            results.push('<span class="text-danger"><i class="fa fa-times"></i> Modal HTML: FAIL</span>');
        }
        
        // Test 2: Check if JavaScript function exists
        if (typeof openTaxComplianceDeactivationModal === 'function') {
            results.push('<span class="text-success"><i class="fa fa-check"></i> JavaScript Function: PASS</span>');
        } else {
            results.push('<span class="text-warning"><i class="fa fa-exclamation-triangle"></i> JavaScript Function: NOT LOADED</span>');
        }
        
        // Test 3: Check CSS styling
        if ($('#taxComplianceDeactivationModal .modal-content').length > 0) {
            results.push('<span class="text-success"><i class="fa fa-check"></i> Modal Structure: PASS</span>');
        } else {
            results.push('<span class="text-danger"><i class="fa fa-times"></i> Modal Structure: FAIL</span>');
        }
        
        // Test 4: Check required files
        var requiredFiles = [
            'views/modals/secure_deactivation.php',
            'views/secure_deactivation_script.php',
            'controllers/Secure.php',
            'helpers/tax_compliance_helper.php'
        ];
        
        results.push('<br><strong>File Structure:</strong>');
        requiredFiles.forEach(function(file) {
            // This would need server-side checking in real implementation
            results.push('<span class="text-info"><i class="fa fa-file"></i> ' + file + ': CHECK MANUALLY</span>');
        });
        
        $('#test-results').html(results.join('<br>'));
    }
    
    function testSecureDeactivation() {
        if (typeof openTaxComplianceDeactivationModal === 'function') {
            openTaxComplianceDeactivationModal();
        } else {
            alert('Secure deactivation function not loaded. Check if the script files are included properly.');
        }
    }
    </script>
</body>
</html>
