<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Migration_Version_100 extends App_module_migration
{
    public function up()
    {
        // Add the tax compliance mode setting if it doesn't exist
        if (!get_option('tax_compliance_mode')) {
            add_option('tax_compliance_mode', '0');
        }
        
        // Add setting for when it was activated (timestamp)
        if (!get_option('tax_compliance_mode_activated_at')) {
            add_option('tax_compliance_mode_activated_at', '');
        }
        
        // Add setting for who activated it (staff ID)
        if (!get_option('tax_compliance_mode_activated_by')) {
            add_option('tax_compliance_mode_activated_by', '');
        }
        
        // Log the migration
        if (file_exists(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php')) {
            $this->ci->load->helper('file');
            $content = "\n" . 'Tax Compliance Mode module migration completed at: ' . date('Y-m-d H:i:s');
            write_file(APPPATH . 'logs/log-migration-' . date('Y-m-d') . '.php', $content, 'a+');
        }
    }

    public function down()
    {
        // Remove the tax compliance mode settings
        delete_option('tax_compliance_mode');
        delete_option('tax_compliance_mode_activated_at');
        delete_option('tax_compliance_mode_activated_by');
    }
}
