<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Tax Compliance Module - Database Migration Script
 * 
 * Run this script to create the necessary database tables for existing installations
 * 
 * <AUTHOR>
 * @link https://github.com/chareen3
 */

// Get CodeIgniter instance
$CI = &get_instance();

echo "<h2>Tax Compliance Module - Database Migration</h2>";
echo "<p>Creating necessary database tables for enhanced security features...</p>";

$errors = [];
$success = [];

try {
    // Create OTP verification table
    if (!$CI->db->table_exists(db_prefix() . 'tax_compliance_otp')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'tax_compliance_otp` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `staff_id` int(11) NOT NULL,
            `otp_code` varchar(6) NOT NULL,
            `email` varchar(255) NOT NULL,
            `ip_address` varchar(45) NOT NULL,
            `user_agent` text,
            `attempts` int(11) DEFAULT 0,
            `max_attempts` int(11) DEFAULT 5,
            `expires_at` datetime NOT NULL,
            `verified` tinyint(1) DEFAULT 0,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `staff_id` (`staff_id`),
            KEY `otp_code` (`otp_code`),
            KEY `expires_at` (`expires_at`),
            KEY `verified` (`verified`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;');
        
        $success[] = "✅ Created table: " . db_prefix() . "tax_compliance_otp";
    } else {
        $success[] = "ℹ️ Table already exists: " . db_prefix() . "tax_compliance_otp";
    }
    
    // Create audit log table
    if (!$CI->db->table_exists(db_prefix() . 'tax_compliance_audit')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'tax_compliance_audit` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `staff_id` int(11) NOT NULL,
            `action` varchar(100) NOT NULL,
            `description` text,
            `ip_address` varchar(45) NOT NULL,
            `user_agent` text,
            `additional_data` text,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `staff_id` (`staff_id`),
            KEY `action` (`action`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;');
        
        $success[] = "✅ Created table: " . db_prefix() . "tax_compliance_audit";
    } else {
        $success[] = "ℹ️ Table already exists: " . db_prefix() . "tax_compliance_audit";
    }
    
    // Add OTP settings
    $settings = [
        'tax_compliance_otp_expiry_minutes' => '10',
        'tax_compliance_max_otp_attempts' => '5',
        'tax_compliance_rate_limit_minutes' => '1',
        'tax_compliance_max_hourly_requests' => '5'
    ];
    
    foreach ($settings as $option => $default_value) {
        if (!get_option($option)) {
            add_option($option, $default_value);
            $success[] = "✅ Added setting: $option = $default_value";
        } else {
            $success[] = "ℹ️ Setting already exists: $option";
        }
    }
    
    // Add initial audit log entry
    if (function_exists('get_staff_user_id') && get_staff_user_id()) {
        $CI->db->insert(db_prefix() . 'tax_compliance_audit', [
            'staff_id' => get_staff_user_id(),
            'action' => 'database_migrated',
            'description' => 'Database tables created for enhanced security features',
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent(),
            'additional_data' => json_encode([
                'version' => '2.0.0',
                'migration_date' => date('Y-m-d H:i:s')
            ])
        ]);
        $success[] = "✅ Added initial audit log entry";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Error: " . $e->getMessage();
}

// Display results
echo "<h3>Migration Results:</h3>";

if (!empty($success)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>Success:</h4>";
    foreach ($success as $message) {
        echo "<p style='color: #155724; margin: 5px 0;'>$message</p>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24; margin-top: 0;'>Errors:</h4>";
    foreach ($errors as $error) {
        echo "<p style='color: #721c24; margin: 5px 0;'>$error</p>";
    }
    echo "</div>";
}

// Test database connectivity
echo "<h3>Database Test:</h3>";
try {
    $test_query = $CI->db->query("SELECT COUNT(*) as count FROM " . db_prefix() . "tax_compliance_otp");
    $result = $test_query->row();
    echo "<p style='color: #155724;'>✅ Database connectivity test passed. OTP table has {$result->count} records.</p>";
    
    $test_query2 = $CI->db->query("SELECT COUNT(*) as count FROM " . db_prefix() . "tax_compliance_audit");
    $result2 = $test_query2->row();
    echo "<p style='color: #155724;'>✅ Audit table test passed. Audit table has {$result2->count} records.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: #721c24;'>❌ Database test failed: " . $e->getMessage() . "</p>";
}

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Verify that all tables were created successfully</li>";
echo "<li>Test the secure deactivation functionality</li>";
echo "<li>Check email configuration for OTP delivery</li>";
echo "<li>Review the module in Admin → Modules</li>";
echo "</ol>";

echo "<p><strong>Note:</strong> This migration script can be run multiple times safely. It will only create tables and settings that don't already exist.</p>";
?>
