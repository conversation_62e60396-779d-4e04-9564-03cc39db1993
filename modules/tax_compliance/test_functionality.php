<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Tax Compliance Module - Functionality Test Script
 * 
 * This script tests the key functionality of the enhanced tax compliance module
 * Run this from the admin panel to verify everything is working correctly
 * 
 * <AUTHOR>
 * @link https://github.com/chareen3
 */

// Load the helper functions
require_once(__DIR__ . '/helpers/tax_compliance_helper.php');

/**
 * Test OTP generation and validation
 */
function test_otp_functionality()
{
    echo "<h3>Testing OTP Functionality</h3>";
    
    // Test OTP generation
    $otp = generate_tax_compliance_otp();
    echo "Generated OTP: " . $otp . " (Length: " . strlen($otp) . ")<br>";
    
    if (strlen($otp) === 6 && is_numeric($otp)) {
        echo "✅ OTP generation: PASS<br>";
    } else {
        echo "❌ OTP generation: FAIL<br>";
    }
    
    // Test OTP storage and verification
    store_tax_compliance_otp($otp, 1); // 1 minute expiry for testing
    
    // Test correct OTP
    $result = verify_tax_compliance_otp($otp);
    if ($result['success']) {
        echo "✅ OTP verification (correct): PASS<br>";
    } else {
        echo "❌ OTP verification (correct): FAIL - " . $result['message'] . "<br>";
    }
    
    // Test incorrect OTP
    $result = verify_tax_compliance_otp('123456');
    if (!$result['success']) {
        echo "✅ OTP verification (incorrect): PASS<br>";
    } else {
        echo "❌ OTP verification (incorrect): FAIL<br>";
    }
    
    echo "<br>";
}

/**
 * Test admin permission checking
 */
function test_admin_permissions()
{
    echo "<h3>Testing Admin Permissions</h3>";
    
    $can_deactivate = can_deactivate_tax_compliance_module();
    
    if (function_exists('is_admin') && is_admin()) {
        if ($can_deactivate) {
            echo "✅ Admin permission check: PASS<br>";
        } else {
            echo "❌ Admin permission check: FAIL<br>";
        }
    } else {
        echo "ℹ️ Admin permission check: Cannot test (not logged in as admin)<br>";
    }
    
    echo "<br>";
}

/**
 * Test language file loading
 */
function test_language_files()
{
    echo "<h3>Testing Language Files</h3>";
    
    // Test if language function exists
    if (function_exists('_l')) {
        $test_keys = [
            'tax_compliance_secure_deactivate',
            'tax_compliance_otp_sent_success',
            'tax_compliance_verification_code'
        ];
        
        $passed = 0;
        foreach ($test_keys as $key) {
            $value = _l($key);
            if ($value !== $key) { // If translation exists, it won't return the key itself
                echo "✅ Language key '$key': PASS<br>";
                $passed++;
            } else {
                echo "❌ Language key '$key': FAIL<br>";
            }
        }
        
        if ($passed === count($test_keys)) {
            echo "✅ All language keys: PASS<br>";
        } else {
            echo "⚠️ Some language keys missing<br>";
        }
    } else {
        echo "ℹ️ Language function not available for testing<br>";
    }
    
    echo "<br>";
}

/**
 * Test email masking function
 */
function test_email_masking()
{
    echo "<h3>Testing Email Masking</h3>";
    
    // Create a simple version of the mask_email function for testing
    function mask_email_test($email) {
        $parts = explode('@', $email);
        if (count($parts) != 2) {
            return $email;
        }
        
        $username = $parts[0];
        $domain = $parts[1];
        
        if (strlen($username) <= 2) {
            return $email;
        }
        
        $masked_username = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
        return $masked_username . '@' . $domain;
    }
    
    $test_cases = [
        '<EMAIL>' => 'ad***@example.com',
        '<EMAIL>' => 'te**@domain.org',
        '<EMAIL>' => '<EMAIL>', // Short username, no masking
    ];
    
    $passed = 0;
    foreach ($test_cases as $input => $expected) {
        $result = mask_email_test($input);
        if ($result === $expected) {
            echo "✅ Email masking '$input': PASS<br>";
            $passed++;
        } else {
            echo "❌ Email masking '$input': FAIL (got '$result', expected '$expected')<br>";
        }
    }
    
    if ($passed === count($test_cases)) {
        echo "✅ All email masking tests: PASS<br>";
    }
    
    echo "<br>";
}

/**
 * Test file structure
 */
function test_file_structure()
{
    echo "<h3>Testing File Structure</h3>";
    
    $required_files = [
        'controllers/Tax_compliance_secure.php',
        'views/modals/secure_deactivation.php',
        'views/secure_deactivation_script.php',
        'views/emails/otp_verification.php',
        'language/english/tax_compliance_lang.php',
        'language/arabic/tax_compliance_lang.php'
    ];
    
    $passed = 0;
    foreach ($required_files as $file) {
        $full_path = __DIR__ . '/' . $file;
        if (file_exists($full_path)) {
            echo "✅ File '$file': EXISTS<br>";
            $passed++;
        } else {
            echo "❌ File '$file': MISSING<br>";
        }
    }
    
    if ($passed === count($required_files)) {
        echo "✅ All required files: PRESENT<br>";
    } else {
        echo "⚠️ Some files are missing<br>";
    }
    
    echo "<br>";
}

/**
 * Run all tests
 */
function run_all_tests()
{
    echo "<h2>Tax Compliance Module - Functionality Tests</h2>";
    echo "<p>Testing enhanced secure deactivation functionality...</p>";
    echo "<hr>";
    
    test_file_structure();
    test_otp_functionality();
    test_admin_permissions();
    test_language_files();
    test_email_masking();
    
    echo "<hr>";
    echo "<h3>Test Summary</h3>";
    echo "<p>✅ = Test Passed | ❌ = Test Failed | ⚠️ = Warning | ℹ️ = Info</p>";
    echo "<p><strong>Note:</strong> Some tests may show 'Cannot test' if not run in the proper CRM environment.</p>";
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'test_functionality.php') {
    run_all_tests();
}
